/*
 * @name main-nav-aside.
 */
body.mnv-opened {
    overflow: hidden;
}

.main-nav-overlay {
    @include trs;
    @include fixed(0, 0, 0, 0);
    @include size(100%, 100vh);
    background: var(--color-1--2);
    opacity: 0;
    visibility: hidden;
    will-change: opacity;
    z-index: 9999;

    &.is-open {
        opacity: 1;
        visibility: visible;
    }
}

.main-nav {
    $this: &;

    &__toggle {
        @include absolute(0, 0);
        z-index: 22;
    }

    &__profile {
        .profile-nav {
            &::before {
                content: none;
            }
        }
    }

    &__container {
        @include trs;
        @include fixed(0, 0, null, 0);
        @include size(100%, 100vh);
        margin: 0 auto;
        overflow-x: hidden;
        overflow-y: auto;
        transform: translateX(120%);
        visibility: hidden;
        will-change: transform;
        z-index: 10000;

        &.is-open {
            transform: translateX(0);
            visibility: visible;
        }
    }

    &__block {
        align-items: stretch;
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        max-width: 1040px;

        @include breakpoint(medium down) {
            flex-direction: column;
            height: 100%;
        }
    }

    &__left {
        flex-grow: 1;
        padding: 67px 10px 40px 0;

        @include breakpoint(medium down) {
            margin: 0 auto;
            min-width: 300px;
            padding: 80px 0 45px;
            width: 588px;
        }

        @include breakpoint(small down) {
            max-width: 320px;
            padding: 60px 0 75px;
            width: 100%;
        }
    }

    &__right {
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        flex-wrap: wrap;
        max-width: 330px;
        padding: 100px 0 40px 87px;
        position: relative;

        @include breakpoint(medium down) {
            align-items: flex-start;
            flex-grow: 1;
            margin: 0 auto;
            max-width: none;
            padding: 62px 0 107px;
            width: 588px;
        }

        @include breakpoint(small down) {
            padding: 61px 0 81px;
            width: auto;
        }

        &::before {
            @include absolute(0, null, 0, 0);
            background-color: var(--color-2--1);
            content: "";
            min-height: 100vh;
            width: 1000vh;
            z-index: -1;

            @include breakpoint(medium down) {
                min-height: 100%;
                transform: translateX(-50%);
            }
        }
    }

    &__right-links {
        @include breakpoint(medium down) {
            display: flex;
            flex-direction: column;
        }

        .btn {
            @include min-size(auto);
            background-color: transparent;
            border: 1px solid $color-black;
            color: $color-black;
            font-weight: var(--fw-medium);
            padding: 22px 44px;

            span {
                letter-spacing: 0;
            }

            @include on-event() {
                color: $color-white;
            }

            &:last-of-type {
                background-color: $color-black;
                color: var(--color-2--1);
                margin-top: 10px;

                @include on-event() {
                    background-color: var(--color-2--3);
                    border-color: var(--color-2--3);
                    color: $color-black;
                }
            }
        }
    }

    &__close-wrap {
        @include fixed(49px, 50px, null, null);
        border: 1px solid $color-black;
        color: $color-black;
        margin: 0;

        @include breakpoint(medium down) {
            border-color: $color-white;
            color: $color-white;
            right: 30px;
            top: 25px;
        }

        @include breakpoint(small down) {
            right: 20px;
            top: 20px;
        }
        
        .btn {
            border: 0;
    
            @include on-event() {
                background-color: $color-black !important;
                border-color: $color-black !important;
                
                span[class*=fa-] {
                    color: $color-white !important;
                }
            }
        }

        span[class*=fa-] {
            color: $color-black;

            @include breakpoint(medium down) {
                color: $color-white;
            }
        }
    }

    &__nav-dropdown {
        display: none;
        width: 100%;

        .is-open > & {
            display: block;
        }

        &.is-level-1 {
            margin-left: 30px;

            @include breakpoint(small down) {
                margin-left: 25px;
            }
        }

        &.is-level-2 {
            @include breakpoint(small down) {
                padding-top: 15px;
            }
        }

        &.is-level-3 {
            margin-left: 35px;
        }
    }

    // Item general styles
    &__nav-item {
        margin-bottom: 10px;

        &:last-of-type {
            @include breakpoint(small down) {
                margin-bottom: 24px;
            }
        }

        .is-level-1 > & {
            padding-top: 8px;
        }

        .is-level-2 > & {
            padding-top: 3px;

            @include breakpoint(small down) {
                padding-top: 0;
            }
        }

        .is-level-3 > & {
            padding-top: 1px;
        }
    }

    &__nav-item-actions {
        align-items: center;
        display: flex;
        margin: 0 40px 4px 20px;
        min-height: 45px;

        @include breakpoint(medium down) {
            margin-left: 0;
        }

        @include breakpoint(small down) {
            margin-bottom: 12px;
        }

        @include fa-icon-style(false) {
            color: $color-white;
            font-size: 1.4rem;
            margin-right: 6px;
        }

        .is-level-1 > #{$this}__nav-item > & {
            border-color: var(--color-1--1);
            margin: 0 70px;

            @include breakpoint(small down) {
                margin: 0 45px 0 5px;
            }
        }

        .is-level-2 > #{$this}__nav-item > & {
            border: 0;
            color: $color-white;
            margin: 0 25px 0 130px;
            min-height: 26px;
            @include icon-before($fa-var-long-arrow-right);

            @include breakpoint(small down) {
                margin: 0 45px 0 35px;
            }

            &::before {
                font-size: 1.2rem;
                font-weight: var(--fw-normal);
            }
        }

        .is-level-3 > #{$this}__nav-item > & {
            border: 0;
            margin: 0 80px 5px;
        }

        .is-open > &,
        [class*="level"] > .is-open > & {
            border-color: transparent;
        }
    }

    &__nav-link {
        @include trs($prop: color);
        @include font(var(--typo-1), 2.8rem, var(--fw-bold));
        color: $color-white;
        display: block;
        line-height: 1;
        margin-left: 78px;
        position: relative;
        text-decoration: none;
        text-transform: uppercase;
        width: auto;

        @include breakpoint(small down) {
            font-size: 2.2rem;
            line-height: 25px;
            margin-left: 58px;
        }

        @include on-event {
            color: var(--color-2--1);
            text-decoration: underline;
        }

        + #{$this}__nav-toggle {
            background-color: $color-white;
            color: $color-black;
            margin-right: -45px;
        }

        .is-level-1 > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            font-size: 2.2rem;
            font-weight: 700;
            line-height: calc(24 / 22);
            margin-left: 57px;
            text-transform: none;

            @include breakpoint(small down) {
                font-size: 1.8rem;
                line-height: 24px;
            }
        }

        .is-level-1 > #{$this}__nav-item > #{$this}__nav-item-actions > #{$this}__nav-toggle + &,
        .is-level-2 > #{$this}__nav-item > #{$this}__nav-item-actions > #{$this}__nav-toggle + & {
            margin-left: 12px;
        }

        .is-level-2 > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            font-size: 1.7rem;
            font-weight: 400;
            line-height: calc(24 / 17);
            margin-left: 15px;
            text-transform: none;

            @include breakpoint(small down) {
                font-size: 1.5rem;
                line-height: 24px;
            }
        }

        .is-level-3 > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            font-size: 1.6rem;
            font-weight: normal;
            margin-left: 10px;
            text-transform: none;
        }

        [data-has-current] > #{$this}__nav-item-actions > & {
            text-decoration: underline;
        }

        .is-open > #{$this}__nav-item-actions > & {
            color: var(--color-2--1);
            text-decoration: underline;
        }
    }

    &__nav-toggle {
        @include trs;
        @include size(45px);
        background-color: var(--color-1--1);
        border: 1px solid var(--color-1--1);
        color: $color-white;
        cursor: pointer;
        display: block;
        flex-shrink: 0;
        order: -1;
        padding: 4px;
        position: relative;

        @include on-event {
            background-color: var(--color-2--1);
            border-color: var(--color-2--1);
        }

        .is-level-1 > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            background-color: transparent;
            border-color: $color-white;
            color: $color-white;

            @include on-event {
                border-color: var(--color-2--1);
                color: var(--color-2--1);
            }
        }

        .is-level-1 > #{$this}__nav-item.is-open > #{$this}__nav-item-actions > & {
            border-color: var(--color-2--1);
            color: var(--color-2--1);
        }

        .is-open > #{$this}__nav-item-actions > & {
            background-color: var(--color-2--1);
            border-color: var(--color-2--1);
            color: var(--color-1--2);
            transform: scaleY(-1);
        }
    }

    &__nav-toggle-icon {
        @include icon-after($fa-var-angle-down);
        @include abs-center;
        color: inherit;
        font-size: 1.2rem;
        font-weight: 400;
    }

    &__list[class] {
        margin-top: 70px;
        position: relative;
        
        &::after,
        &::before {
            @include size(47px, 18px);
            @include absolute(null, null, null, 1px);
            background-image: image('icons/black-deco-title.svg');
            background-repeat: no-repeat;
            background-size: contain;
            content: "";
        }

        &::before {
            top: -42px;
        }
        
        &::after {
            bottom: -24px;
        }
    }

    &__link {
        color: $color-black;
        display: inline-flex;
        font-size: 2rem;
        font-weight: 500;
        line-height: calc(28 / 20);
        margin: 0 0 10px;
        position: relative;
        text-decoration: none;

        @include on-event {
            #{$this}__link-text {
                text-decoration: underline;
            }
        }

        svg {
            @include size(1.1em);
            @include absolute(1px, null, null 0);
            display: block;
            fill: $color-black;
        }

        @include fa-icon-style {
            position: absolute;
            top: 3px;
        }
    }

    &__link-text {
        padding-left: 36px;
    }

    &__lang {
        @include breakpoint(large) {
            display: none;
        }

        @include breakpoint(medium down) {
            @include absolute(25px, null, null, 80px);
        }

        @include breakpoint(small down) {
            left: 11px;
            top: 20px;
        }

        .lang,
        .g-translate-dropdown__toggle {
            color: $color-white;
        }
    }
}
