//genial decor for home style
@mixin home-styled-decor {
    &::before {
        @include absolute(0, null, null, 50%);
        @include size(1000vw, calc(100% - 111px));
        background-color: $color-white;
        content: "";
        transform: translateX(-500vw);
        z-index: -1;

        @include breakpoint(medium down) {
            background-color: var(--color-1--2);
            height: 100%;
        }

        @include breakpoint(small down) {
            height: calc(100% + 20px);
            top: -20px;
        }
    }
}

.event-focus {
    $this: &;
    padding-bottom: 10px;
    position: relative;

    @include breakpoint(medium down) {
        padding-bottom: 20px;
    }

    // styles for HP with background
    .events-home & {
        #{$this} {
            &__text {
                background-color: $color-white;
                padding: 35px 71px 48px;
                width: 409px;

                @include breakpoint(medium down) {
                    padding: 21px 56px 21px 70px;
                    text-align: center;
                    width: 100%;
                }

                @include breakpoint(small down) {
                    padding: 20px 55px;
                }
            }

            &__title {
                .underline {
                    @include breakpoint(medium down) {
                        @include multiline-underline();
                    }
                }

                &.item-title {
                    font-size: 3rem;

                    @include breakpoint(medium down) {
                        font-size: 2rem;
                    }
                }
            }

            &__time-place {
                .time-place__item {
                    @include breakpoint(medium down) {
                        color: var(--color-1--3);
                    }
                }
            }

            &__date {
                background-color: var(--color-1--1);
                color: $color-white;
                margin-top: 0;

                &::before {
                    content: none;
                }

                &.date.is-large {
                    @include min-size(230px, auto);
                    padding: 17px 19px 12px 27px;

                    @include breakpoint(medium down) {
                        margin-top: 0;
                    }

                    @include breakpoint(small down) {
                        padding: 15px 23px;
                    }

                    .date {
                        &__time {
                            font-size: 1.8rem;

                            @include breakpoint(small down) {
                                font-size: 1.4rem;
                                line-height: 18px;
                            }
                        }

                        &__icon {
                            color: var(--color-2--1);
                            font-size: 1.8rem;
                        }

                        &__item {
                            color: $color-white;
                            font-size: 1.8rem;

                            @include breakpoint(small down) {
                                font-size: 1.4rem;
                                line-height: 18px;
                            }
                        }

                        &__wrap {
                            display: block;
                            line-height: 22px;
                            padding-left: 0;
                        }
                    }
                }
            }

            &__picture-link {
                flex-shrink: 0;
                overflow: hidden;
                width: 792px;
                z-index: -1;

                @include breakpoint(medium down) {
                    width: 100%;
                }

                @include breakpoint(small down) {
                    @include size(320px, 240px);
                }
            }

            &__wrapper {
                @extend %link-block-context;
                align-items: flex-start;
                display: flex;
                flex-direction: column-reverse;
                position: relative;
                z-index: 1;

                @include breakpoint(medium down) {
                    flex-direction: column-reverse;
                }

                @include breakpoint(small down) {
                    padding: 0;
                }

                @include on-event() {
                    #{$this}__title-link {
                        .underline {
                            background-size: 100% 100%;
                        }
                    }
                }
            }

            &__content {
                align-items: flex-start;
                background-color: transparent;
                flex-direction: column-reverse;
                flex-grow: 1;
                height: 100%;
                margin-left: -1px;
                margin-top: -132px;
                padding: 0;
                position: relative;
                word-break: break-word;

                @include breakpoint(medium down) {
                    align-items: center;
                    margin: -85px auto 0;
                    width: 480px;
                }

                @include breakpoint(small down) {
                    align-items: flex-start;
                    flex-direction: column-reverse;
                    width: 320px;
                }

                .swiper-slide-active & {
                    display: flex;

                    @include breakpoint(small down) {
                        align-items: flex-start;
                    }
                }
            }
        }
    }

    &__wrapper {
        @extend %link-block-context;
        align-items: center;
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
        position: relative;
        z-index: 1;

        @include breakpoint(medium down) {
            flex-direction: column-reverse;
        }

        @include breakpoint(small down) {
            padding: 0;
        }

        @include on-event() {
            #{$this}__title-link {
                .underline {
                    background-size: 100% 100%;
                }
            }
        }
    }

    &__title {
        &.item-title {
            font-size: 3rem;

            @include breakpoint(medium down) {
                font-size: 2.8rem;
                text-align: center;
            }
        }
    }

    &__picture-link {
        flex-shrink: 0;
        overflow: hidden;
        width: 792px;
        z-index: -1;

        @include breakpoint(medium down) {
            width: 100%;
        }
    }

    &__picture {
        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__content {
        align-items: flex-start;
        background-color: $color-white;
        flex-direction: column-reverse;
        flex-grow: 1;
        height: 100%;
        margin-left: -110px;
        max-width: 422px;
        padding: 40px 50px;
        position: relative;
        word-break: break-word;

        @include breakpoint(medium down) {
            align-items: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin: -74px 0 0;
            padding: 38px 36px;
            width: 563px;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            margin: 0;
            padding: 8px 0;
            width: 320px;
        }

        .swiper-slide-active & {
            display: flex;

            @include breakpoint(small down) {
                align-items: flex-start;
            }
        }
    }

    &__date {
        background-color: transparent;
        margin-top: 22px;

        &.date.is-large {
            min-height: auto;
            padding: 0;

            @include breakpoint(medium down) {
                margin-top: 10px;
            }

            &::before {
                @include size(5px, 25px);
                left: 0;
                rotate: 16deg;
                top: 4px;
                transform: none;

                @include breakpoint(medium down) {
                    top: 0;
                }
            }

            .date {
                &__wrap {
                    align-items: center;
                    display: flex;
                    padding-left: 20px;

                    @include breakpoint(medium down) {
                        padding-left: 14px;
                    }
                }

                &__time {
                    line-height: 18px;
                }

                &__item {
                    color: $color-black;
                    font-size: 1.6rem;
                    line-height: 28px;
                }

                &__icon {
                    color: $color-black;
                    font-size: 1.6rem;
                    line-height: 18px;
                    margin: 2px 5px 0;
                }
            }
        }

        @include breakpoint(medium down) {
            margin: 0;
        }

        @include breakpoint(small down) {
            margin: -73px 0 0 0;
        }
    }

    &__text {
        @include breakpoint(medium down) {
            min-height: 108px;
            padding: 0;
            width: 100%;
        }

        @include breakpoint(small down) {
            padding: 0;
            width: auto;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        @include font(null, 1.1rem, var(--fw-medium));
        color: var(--color-1--1);
        margin-top: 10px;
        z-index: 2;
    }

    &__time-place {
        display: flex;
        margin-top: 10px;

        @include breakpoint(medium down) {
            justify-content: center;
            margin-top: 15px;
        }

        .time-place__item {
            font-size: 1.1rem;
            margin-right: 16px;

            &.is-time {
                flex-shrink: 0;
            }

            @include breakpoint(medium down) {
                font-size: 1.2rem;
                font-weight: var(--fw-medium);
            }
        }
    }

    &__actions {
        @include absolute(35px, 45px);
        z-index: 11;
    }
}
