.header-wrapper {
    $this: &;

    display: block;
    width: 100%;

    &__container {
        @extend %container-fluid;
        margin: 0 auto;
        padding: 0;
        position: relative;

        @include breakpoint(medium down) {
            max-width: 100%;
        }
    }

    .has-page-image &,
    body.home-page &,
    body.home-hospital-page & {
        z-index: 100;
    }
}

.header {
    $this: &;

    // @include trs(background);
    height: 160px;
    opacity: 1;
    padding: 0;
    z-index: 20;

    @include breakpoint(medium down) {
        height: 95px;
        padding: 15px 26px;

        &.js-fixed-el-abs {
            left: 0;
            transform: none;
        }
    }

    @include breakpoint(small down) {
        height: 75px;
    }

    &__inner {
        align-items: center;
        display: flex;
        justify-content: center;
        margin: 29px auto 0;
        max-width: 1200px;
        min-height: auto;
        padding: 0;

        @include breakpoint(medium down) {
            margin-top: 0;
            min-height: 65px;
        }

        @include breakpoint(small down) {
            min-height: 45px;
        }
    }

    &__profile {
        @include size(149px, 53px);
        margin-right: auto;

        @include breakpoint(medium down) {
            display: none;
        }
    }

    &__logo {
        @include size(220px, 38px);
        @include trs;
        align-content: center;
        align-items: center;
        display: flex;
        margin-right: 42px;
        padding: 0;

        @include breakpoint(medium down) {
            @include size(194px, 34px);
            margin-right: auto;
        }

        @include breakpoint(small down) {
            @include size(135px, 23px);
        }

        #{$this}:not(.js-fixed-el) & {
            body:not(.is-mnv-opened) .has-page-image &,
            body:not(.is-mnv-opened).home-page &,
            body:not(.is-mnv-opened).home-hospital-page & {
                a.logo {
                    color: $color-white;
                }
            }
        }
    }

    &__components {
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        width: calc(100% - 262px);

        @include breakpoint(medium down) {
            width: auto;
        }
    }

    &__actions {
        align-items: center;
        display: flex;
        height: 80px;
        margin-right: 35px;

        @include breakpoint(medium down) {
            height: 65px;
        }

        @include breakpoint(small down) {
            display: none;
        }
    }

    &__lang {
        @include size(54px, 80px);
        align-items: center;
        display: flex;
        margin-right: 35px;

        @include breakpoint(medium down) {
            display: none;
        }
    }

    &__nav {
        align-items: center;
        display: flex;
        justify-content: center;
        width: 1%;
    }

    &.has-nav-bottom {
        background-color: $color-white;
        
        @include breakpoint(large) {
            padding-bottom: 0;
        }

        #{$this}__inner {
            @include breakpoint(large) {
                flex-wrap: wrap;
            }
        }

        #{$this}__nav {
            @include breakpoint(large) {
                @include size(666px, fit-content);
                margin: 0 54px 0 0;
                min-height: auto;
                position: relative;
            }

            @include breakpoint(medium down) {
                display: none;
            }
        }
    }

    &.js-fixed-el {
        background-color: $color-white;
    }

    &:not(.js-fixed-el) {
        body:not(.is-mnv-opened) .has-page-image:not(.has-secondary-heading) &,
        body:not(.is-mnv-opened).home-page &,
        body:not(.is-mnv-opened).home-hospital-page & {
            .header__actions {
                .btn {
                    background-color: transparent;
                    border-color: $color-white;
                    color: $color-white;

                    @include on-event {
                        background-color: var(--color-1--1);
                        border-color: var(--color-1--1);
                        color: $color-white;
                    }
                }
            }
        }
    }

    &:not(.js-fixed-el).has-logo-center {
        #{$this}__logo {
            @include absolute(20px, 50%);
            transform: translate(50%, 100%);

            @include breakpoint(medium down) {
                top: 125px;
            }

            @include breakpoint(small down) {
                top: 75px;
            }
        }

        #{$this}__actions {
            display: flex;
            margin-right: auto;

            @include breakpoint(small down) {
                height: 45px;
            }
        }
    }
}

.subheader {
    @include size(100%, 50px);
    background-color: $color-black;
    display: flex;
    justify-content: flex-end;
    margin: 0 auto;
    max-width: 1200px;
    position: relative;

    &::before {
        @include size(1000vw, 100%);
        @include absolute(0, 0, null, 0);
        background-color: $color-black;
        content: "";
        transform: translate(-50%);
    }

    .btn {
        @include min-size(auto);
        font-weight: var(--fw-medium);
        padding: 0;

        span {
            letter-spacing: 0;
        }

        &:last-of-type {
            color: var(--color-2--1);
            margin-left: 28px;
        }
    }

    @include breakpoint(medium down) {
        display: none;
    }
}
