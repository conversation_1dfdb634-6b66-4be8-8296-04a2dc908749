{% from 'views/utils/utils.njk' import svg %}
{%- from 'views/core-components/title.njk' import TitleRTE, TitleDefault -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    Key Box item
#}
{%- macro KeyBoxItem(icon) -%}
    <div class="key-box-item">
        <div class="key-box-item__icon" aria-hidden="true">
            {{ svg(icon, 145, 145) }}
        </div>
        <a href="#" class="key-box-item__content">
            <div class="key-box-item__title-wrapper">
                <p class="key-box-item__title"><strong>{{ range(32, 5000000) | random }} €</strong></p>
            </div>
            <p class="key-box-item__description">
                {{ lorem(1) }}
            </p>
        </a>
    </div>
{%- endmacro -%}

{#
    Key Box content items
    @param {string} titleText - section title
    @param {icons} itemsCount - icons array
#}
{%- macro KeyBoxContentBlocks(
    titleText = 'Titre chiffre clé 3 blocs',
    icons = ['icons/man-and-trash', 'icons/cone', 'icons/man-and-message']
) -%}
    {% call Section(className = 'key-box', container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            <ul class="list is-columns-3 is-columns-md-1 is-columns-sm-1">
                {%- for icon in icons -%}
                    <li class="list__item">
                        {{ KeyBoxItem(
                            icon = icon
                        ) }}
                    </li>
                {%- endfor -%}
            </ul>
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    Key Box content items
    @param {string} titleText - section title
    @param {icons} itemsCount - icons array
#}
{%- macro KeyBoxHomeBlocks(
    titleText = 'CHIFFRES CLÉS',
    icons = ['icons/man-and-trash', 'icons/cone', 'icons/man-and-message']
) -%}
    {% call Section(className = 'key-box-home', container = "key-box-home__container") %}
        {% if titleText %}
            <div class="section__title">
                {{ TitleDefault(
                    text = titleText
                ) }}
            </div>
        {% endif %}
        <div class="section__content">
            <ul class="list is-columns-3 is-columns-md-1 is-columns-sm-1">
                {%- for icon in icons -%}
                    <li class="list__item">
                        {{ KeyBoxItem(
                            icon = icon
                        ) }}
                    </li>
                {%- endfor -%}
            </ul>
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    Key Box content one column item with rte
#}
{%- macro KeyBoxOneColumnContentBlock() -%}
    {% call Section(className = 'key-box', container = false) %}
        <div class="section__content">
            <div class="flex-row">
                <div class="col-md-12 col-lg-8">
                    <div class="rte">
                        <p>{{ lorem(4) }}</p>
                        <p>{{ lorem(20) }}</p>
                    </div>
                </div>
                <div class="col-md-12 col-lg-4">
                    <div class="key-box">
                        {{ KeyBoxItem(
                            icon = 'icons/cone'
                        ) }}
                    </div>
                </div>
            </div>
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    KeyBoxSG template
    Styleguide template.
    @param {string} sectionId - section id.
    @param {string} title - section title.
    @param {icons} itemsCount - icons array
#}
{%- macro KeyBoxSG(sectionId = 'key-box-columns', title = 'Titre chiffre clé 3 blocs', icons = ['icons/man-and-trash', 'icons/cone', 'icons/man-and-message']) -%}
    {% call SG.Section(sectionId) %}
        <h2 class="styleguide-section__title">{{ title }}</h2>
        {%- call SG.Preview() -%}
            <div class="key-box">
                <div class="list is-columns-3 is-columns-md-1 is-columns-sm-1">
                    {%- for icon in icons -%}
                        <div class="list__item">
                            {{ KeyBoxItem(
                                icon = icon
                            ) }}
                        </div>
                    {%- endfor -%}
                </div>
            </div>
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

{#
    KeyBoxOneColumnSG template
    Styleguide template
    @param {string} sectionId - section id.
    @param {string} title - section title.
#}
{%- macro KeyBoxOneColumnSG(sectionId = 'key-box-one-column', title = 'Titre chiffre clé 1 block') -%}
    {%- call SG.Section(sectionId) -%}
        <h2 class="styleguide-section__title">{{ title }}</h2>
        {%- call SG.Preview() -%}
            <div class="flex-row">
                <div class="col-md-12 col-lg-8">
                    <div class="rte">
                        <p>{{ lorem(4) }}</p>
                        <p>{{ lorem(20) }}</p>
                    </div>
                </div>
                <div class="col-md-12 col-lg-4">
                    <div class="key-box">
                        {{ KeyBoxItem(
                            icon = 'icons/cone'
                        ) }}
                    </div>
                </div>
            </div>
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}
