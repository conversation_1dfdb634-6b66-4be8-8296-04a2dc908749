{%- from 'views/core-components/list.njk' import List -%}

{#
    JobItem template.
    @param {object} settings - user settings object
#}
{%- macro JobItem(settings = {}) -%}
    <article class="job-item">
        <h3 class="item-title job-item__title">
            <span class="theme job-item__category">{{ lorem(2, 'word') }}</span>
            <span class="sr-only">:</span>
            <a href="single-job.html" class="underline job-item__title-link" >
                <span class="underline">{{ lorem(1) }}</span>
            </a>
        </h3>
        <div class="job-item__content">
            <p class="publication is-large job-item__publication">
                <span>Postuler avant le </span>
                <time datetime="2022-09-26">26 septembre 2022</time>
            </p>
            <p class="item-teaser job-item__teaser">{{ lorem(3) | truncate(200, true, "...") }}</p>
        </div>
        <p class="publication is-primary job-item__publication">
            <span>Publié le </span>
            <time datetime="2022-09-26">26 septembre 2022</time>
        </p>
    </article>
{%- endmacro -%}

{#
    JobList template.
    @param {string} itemClass - item class modifier.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
#}
{%- macro JobList(
    itemClass = 'has-mb-1',
    count = 10,
    cols = 1
) -%}
    {% call List(
        itemClass = itemClass,
        count = count,
        cols = cols
    ) %}
        {{ JobItem() }}
    {% endcall %}
{%- endmacro -%}
