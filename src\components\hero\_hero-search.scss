.hero-search {
    box-shadow: 0 0 16px rgba($color-black, 0.35);

    &__btn {
        @include trs;
        @include size(95px, 80px);
        background-color: $color-white;
        border: none;
        clip-path: polygon(33% 0, 100% 0, 100% 100%, 0% 100%);
        color: var(--color-1--2);
        cursor: pointer;
        font-size: 2rem;
        margin-left: -1px;
        position: relative;

        &::before {
            @include size(3px, 34px);
            @include absolute(24px, null, null, 15px);
            background-color: var(--color-2--1);
            content: '';
            rotate: 22deg;
            z-index: 1;

            @include breakpoint(medium down) {
                left: 3px;
                top: 14px;
            }

            @include breakpoint(small down) {
                left: -3px;
            }
        }

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        @include breakpoint(medium down) {
            @include size(73px, 63px);
        }

        @include breakpoint(small down) {
            width: 63px;
        }
    }

    .form {
        &__controls-group {
            display: flex;
            margin-bottom: 0;
        }

        &__field-wrapper {
            flex-grow: 1;
            position: relative;
            width: 1%;

            &::before {
                @include absolute();
                @include size(100%);
                background-color: $color-white;
                content: '';
                margin-left: 31px;
                pointer-events: none;
                z-index: -1;
            }

            &.js-autocomplete.is-visible {
                .form__field {
                    color: $color-black;
                }
            }

            .js-autocomplete-input-clear {
                @include breakpoint(small down) {
                    right: 10px;
                }
            }
        }

        &__field {
            border: none;
            color: var(--color-1--2);
            font-size: 2.2rem;
            height: 80px;
            padding: 15px 55px 15px 42px;

            @include breakpoint(medium down) {
                font-size: 1.8rem;
                height: 63px;
                padding: 10px 20px 10px 25px;
            }

            @include breakpoint(small down) {
                font-size: 1.6rem;
            }

            &::-ms-clear {
                display: none;
            }

            &:focus ~ .form__field-placeholder,
            &:not(:focus):valid ~ .form__field-placeholder {
                font-size: 1.2rem;
                left: 5px;
                top: 0;
                transform: translateY(0);
            }
        }

        &__field-placeholder {
            @include trs;
            @include absolute(50%, 20px, null, 42px);
            color: var(--color-1--2);
            font-size: 2.2rem;
            overflow: hidden;
            pointer-events: none;
            text-overflow: ellipsis;
            transform: translateY(-50%);
            white-space: nowrap;

            @include breakpoint(medium down) {
                font-size: 1.8rem;
                left: 25px;
            }

            @include breakpoint(small down) {
                font-size: 1.6rem;
            }
        }
    }
}
