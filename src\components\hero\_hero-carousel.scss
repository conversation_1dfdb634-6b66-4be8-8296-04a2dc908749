.hero-carousel {
    $this: &;
    position: relative;

    &__wrapper {
        display: flex;
    }

    &__container {
        overflow: visible !important;
    }

    &__control {
        @include trs();
        @include absolute(auto !important, null, 179px, null);
        @include size(40px);
        align-items: center;
        background: none;
        border: 4px solid $color-black;
        border-radius: 50%;
        color: $color-white;
        cursor: pointer;
        display: flex;
        justify-content: center;
        overflow: hidden;
        padding: 0;
        transform: translateY(-50%);
        z-index: 2;

        @include breakpoint(medium down) {
            @include size(30px);
            bottom: auto;
            top: 714px !important;
        }

        @include breakpoint(small down) {
            top: 502px !important;
        }

        @include fa-icon-style(false) {
            color: inherit;
            font-size: 3rem;
            font-weight: var(--fw-bold);
            vertical-align: middle;
        }

        @include on-event {
            background-color: var(--color-1--1);
            border-color: var(--color-1--1);

            span[class*=fa-] {
                color: $color-white;
            }
        }

        &.is-prev {
            right: 254px;

            @include breakpoint(medium down) {
                left: 99px;
                right: auto;
            }

            @include breakpoint(small down) {
                left: 20px;
            }
        }

        &.is-next {
            right: 205px;

            @include breakpoint(medium down) {
                left: 138px;
                right: auto;
            }

            @include breakpoint(small down) {
                left: 60px;
            }
        }

        span[class*=fa-] {
            color: $color-black;
            font-size: 2rem;
            font-weight: var(--fw-black);

            @include breakpoint(medium down) {
                font-size: 1.6rem;
            }
        }

        &.swiper-button-disabled {
            opacity: 0.3;
        }
    }

    .swiper-pagination {
        align-items: center;
        display: inline-flex;
        margin: 0;
        width: auto;
        z-index: 10;

        &__bullet {
            @include trs(opacity);
            display: block;
            margin-right: 6px;

            &.is-active {
                .swiper-pagination__bullet-btn {
                    @include size(40px, 15px);
                    background-color: var(--color-1--1);
                    border-color: var(--color-1--1);
                    border-radius: 0;

                    @include breakpoint(medium down) {
                        @include size(26px, 10px);
                    }
                }
            }

            &:last-of-type {
                margin-right: 20px;

                @include breakpoint(medium down) {
                    margin-right: 0;
                }
            }

            &:not(.is-active) {
                @include on-event {
                    opacity: 0.7;
                }
            }
        }

        &__bullet-btn {
            @include trs;
            @include size(15px);
            background-color: transparent;
            border: 1px solid $color-3--4;
            cursor: pointer;
            display: block;
            padding: 0;
            position: relative;

            @include breakpoint(medium down) {
                @include size(10px);
            }

            &::before {
                @include absolute(-6px, null, null, -4px);
                @include size(calc(100% + 8px), calc(100% + 12px));
                content: '';
            }
        }
    }

    &__actions {
        display: inline-flex;
        padding: 1px 2px 0;
    }

    & &__action {
        
        @include trs(opacity);
        background: none;
        border: 0;
        color: $color-black;
        cursor: pointer;
        display: inline-block;
        font-size: 1.5rem;
        padding: 0;

        @include on-event {
            opacity: 0.7;
        }

        &.is-active {
            color: var(--color-2--1);
            display: none;
        }

        span {
            pointer-events: none;

            &::before {
                font-weight: var(--fw-bold);
            }
        }
    }

    &__group {
        @include absolute(null, 36px, 189px, null);
        align-items: center;
        display: flex;
        flex-direction: row-reverse;
        z-index: 5;

        @include breakpoint(medium down) {
            bottom: auto !important;
            top: 724px !important;
        }

        @include breakpoint(small down) {
            left: 110px !important;
            top: 510px !important;
        }

        &.has-shadow {
            &::before {
                @include absolute(50%, null, null, 50%);
                @include size(calc(100% + 100px));
                background: radial-gradient(closest-side at 50% 50%, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.52) 24%, rgba(0, 0, 0, 0.15) 71%, rgba(0, 0, 0, 0) 100%) 0 0 no-repeat padding-box;
                content: '';
                pointer-events: none;
                transform: translate(-50%, -50%);
                z-index: -1;
            }
        }
    }
}
