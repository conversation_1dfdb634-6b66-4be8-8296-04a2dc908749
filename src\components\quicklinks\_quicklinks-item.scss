.quicklink-item {
    $this: &;

    @extend %link-block-context;
    align-items: center;
    display: flex;
    flex-direction: column;
    padding: 5px;

    @include breakpoint(medium down) {
        padding: 3px;
    }

    @include breakpoint(small down) {
        flex-direction: row;
        justify-content: center;
    }

    &__svg-wrapper {
        @include size(60px);
        flex-shrink: 0;

        @include breakpoint(small down) {
            @include size(36px);
            margin-right: 20px;
        }

        svg {
            @include size(100%);
            fill: var(--color-1--1);
        }
    }

    &__text {
        @extend %link-block;
        @extend %underline-context;
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: var(--color-1--1);
        line-height: calc(18 / 14);
        margin: 29px 0 0 0;
        max-width: 240px;

        .page-content & {
            margin-top: 22px;

            @include breakpoint(small down) {
                margin-top: 0;
            }
        }

        @include breakpoint(small down) {
            margin: 0;
        }

        .underline {
            @include multiline-underline();
        }

        &:focus-visible {
            &::after {
                outline-offset: -2px;
            }
        }
    }

    &__subtitle {
        @extend %link-block;
        @extend %underline-context;
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        color: $color-black;
        flex-grow: 1;
        line-height: 24px;
        margin: 10px 0 0 0;
        max-width: 205px;
        padding-right: 10px;

        @include breakpoint(medium down) {
            font-size: 1.3rem;
            margin: -5px 0 0 15px;
            padding-right: 0;
        }

        @include breakpoint(small down) {
            line-height: 16px;
            margin: 0;
        }

        .underline {
            @include multiline-underline();
        }

        &:focus-visible {
            &::after {
                outline-offset: -2px;
            }
        }
    }

    &__teaser {
        @extend %link-block;
        @extend %underline-context;
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        color: $color-black;
        flex-grow: 1;
        line-height: 24px;
        margin: 10px 0 0 -5px;
        padding-right: 25px;

        @include breakpoint(medium down) {
            font-size: 1.3rem;
            margin: -5px 0 0 15px;
            padding-right: 0;
        }

        @include breakpoint(small down) {
            line-height: 16px;
            margin: 0;
        }

        .underline {
            @include multiline-underline();
        }

        &:focus-visible {
            &::after {
                outline-offset: -2px;
            }
        }
    }

    .is-inverted & {
        #{$this}__svg-wrapper {
            svg {
                fill: $color-white;
            }
        }

        #{$this}__text {
            color: $color-white;

            .underline {
                @include multiline-underline();
            }
        }
    }
}
