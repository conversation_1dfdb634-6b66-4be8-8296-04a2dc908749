@mixin gradient($useTop: true, $useBottom: true) {
    @if $useTop {
        &::before {
            @include absolute(0, 0);
            @include size(100%, 310px);
            content: '';
            pointer-events: none;
            z-index: 1;

            @include breakpoint(medium down) {
                height: 226px;
            }

            @include breakpoint(small down) {
                height: 120px;
            }
        }
    }

    @if $useBottom {
        &::after {
            @include absolute(null, 0, 0);
            @include size(100%, 481px);
            background-image: linear-gradient(180deg, rgba($color-black, 0) 0%, rgba($color-black, 0.52) 64%, rgba($color-black, 0.6) 100%);
            content: '';
            pointer-events: none;

            @include breakpoint(medium down) {
                height: 334px;
            }

            @include breakpoint(small down) {
                height: 247px;
            }
        }
    }
}

.hero-item {
    $this: &;

    min-width: 100%;
    position: relative;

    @include breakpoint(medium down) {
        display: flex;
        flex-direction: column;
    }

    &::after {
        @include absolute(0, 0, null, null);
        @include size(590px, 610px);
        background-color: var(--color-2--1);
        clip-path: polygon(48% 0, 100% 0, 100% 100%, 0% 100%);
        content: '';
        opacity: 0.9;

        @include breakpoint(medium down) {
            @include size(100%, 364px);
            bottom: auto;
            clip-path: polygon(0 36%, 100% 0, 100% 100%, 0% 100%);
            opacity: 1;
            top: 453px;
        }

        @include breakpoint(small down) {
            clip-path: polygon(0 17%, 100% 0, 100% 100%, 0% 100%);
            height: 317px;
            top: 303px;
        }
    }

    .swiper-slide-active &,
    .has-video & {
        &__video {
            opacity: 1;
            visibility: visible;
        }
    }

    &.swiper-slide:not(.swiper-slide-active) {
        z-index: -1;
    }
    

    &__media {
        @include size(100%, 610px);
        max-height: 100vh;
        overflow: hidden;
        position: relative;
        
        @include breakpoint(medium down) {
            @include size(100%, 593px);
            min-height: auto;
        }
        
        @include breakpoint(small down) {
            height: 374px;
            max-height: none;
        }

        .has-video & {
            @include responsive-ratio(1920, 1080, 'before');

            @include breakpoint(medium down) {
                @include responsive-ratio(768, 620, 'before');
                max-height: 620px;
            }

            @include breakpoint(small down) {
                @include responsive-ratio(360, 475, 'before');
                max-height: 475px;
            }
        }
    }

    &__link {
        display: block;

        @include breakpoint(small down) {
            height: 100%;
        }

        .lazy.lazyloaded:not(.is-no-image) {
            animation: none !important;
        }

        &:hover {
            @at-root {
                a#{&} {
                    img {
                        transform: scale(1.2);
                    }
                }
            }
        }

        &:focus,
        &:focus-within {
            #{$this}__picture {
                outline: 4px solid var(--color-1--1);
                outline-offset: -6px;
            }

            img {
                transform: none;
            }
        }
    }

    &__picture {
        @include trs;
        @include gradient;
        display: block;
        height: 100%;
        position: relative;

        img {
            @include trs;
            @include object-fit;
            @include size(100%, 610px);

            @include breakpoint(medium down) {
                height: 593px;
            }

            @include breakpoint(small down) {
                height: 374px;
            }
        }
    }

    &__video {
        @include gradient;
        @include trs;
        @include abs-center;
        @include size(100%);
        opacity: 0;
        overflow: hidden;
        visibility: hidden;

        .video {
            background-color: $color-black;
            height: 100%;
            position: relative;

            @include on-event {
                .video-controls__action {
                    opacity: 1;
                }
            }

            &__video-wrap {
                display: block;
                height: 100%;
                overflow: hidden;
            }

            &__controls-wrap {
                @include absolute(null, null, 50%, 50%);
                transform: translate(-50%, -50%);
                z-index: 2;
            }

            .video-controls {
                &__action {
                    @include trs;
                    @include size(50px);
                    background-color: var(--color-1--1);
                    border: 0;
                    border-radius: 50%;
                    color: rgba($color-white, 0.8);
                    cursor: pointer;
                    display: block;
                    opacity: 0;
                    padding: 0;
                    z-index: 1;

                    &:focus {
                        opacity: 1;
                    }
                }

                &__icon {
                    pointer-events: none;
                }
            }
        }

        video {
            @include object-fit;
            @include absolute(0, 0, 0, 0);
            @include size(100%);
            margin: auto;
        }
    }

    &__title {
        display: block;
    }

    &__search {
        @include absolute(null, null, 50px, 49px);
        width: 574px;
        z-index: 5;

        @include breakpoint(medium down) {
            margin: 188px auto 0;
            order: 2;
            position: static;
        }

        @include breakpoint(small down) {
            bottom: 70px;
            margin-top: 216px;
            max-width: 574px;
            padding: 0 20px;
            width: 100%;
        }
    }

    &__keywords[class] {
        margin-top: 10px;

        @include breakpoint(medium down) {
            margin-top: 20px;
        }

        @include breakpoint(small down) {
            margin-top: 15px;
        }
    }
}
