.menu-cross {
    $this: &;

    &__link {
        @include font(var(--typo-1), 1.3rem, var(--fw-normal), normal);
        @include trs;
        @include focus-outline($offset: 2px);
        background-color: transparent;
        border: 0;
        color: $color-black;
        cursor: pointer;
        display: flex;
        letter-spacing: 1.04px;
        padding: 0;
        position: relative;
        text: {
            align: center;
            decoration: none;
            transform: uppercase;
        }
        width: 100%;

        &[aria-current="page"],
        &:hover,
        &:focus {
            color: var(--color-1--1);

            #{$this}__text {
                text-decoration: underline;
            }
        }

        @include add-inverted-styles {
            color: $color-white;

            &[aria-current="page"],
            &:hover,
            &:focus {
                color: var(--color-2--1);
            }
        }

        &::after {
            @include absolute(50%, -15px, null, null);
            @include size(1px, 14px);
            background-color: var(--color-2--1);
            content: "";
            rotate: 25deg;
            transform: translateY(-50%);
        }
    }

    &__list[class] {
        display: flex;
        flex-wrap: wrap;

        @include breakpoint(small down) {
            justify-content: center;
        }
    }

    &__item {
        margin: 0 34px 0 0;
        padding: 0;

        @include breakpoint(medium down) {
            margin-top: 15px;
        }

        @include breakpoint(small down) {
            margin: 0 34px 10px 0;
        }

        &:last-of-type {
            margin-right: 0;

            #{$this}__link {
                &::after {
                    content: none;
                }
            }
        }
    }

    &__text {
        color: inherit;
        flex-grow: 1;
        font: inherit;
    }
}
