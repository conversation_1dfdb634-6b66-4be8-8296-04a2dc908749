// FLASH INFO
.flash-info-item {
    $this: &;

    align-items: center;
    display: flex;

    @include breakpoint(medium down) {
        align-items: flex-start;
        flex-wrap: wrap;
        justify-content: center;
    }

    &__wrapper {
        display: flex;

        @include breakpoint(small down) {
            display: block;
        }
    }

    &__image {
        display: block;
        flex-shrink: 0;
        margin-right: 20px;

        @include breakpoint(small down) {
            margin: 0 0 20px;
        }

        img {
            display: block;
            margin: 0 auto;
        }
    }

    &__content {
        flex: 1 0 auto;
        margin: 0;
        padding: 0;
        width: 1%;

        @include breakpoint(small down) {
            text-align: center;
            width: auto;
        }
    }

    &__title {
        @include font(var(--typo-1), 1.8rem, var(--fw-bold));
        color: $color-white;
        line-height: 2.2rem;
        margin: 0 0 5px;
    }

    &__teaser {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-white;
        line-height: 1.8rem;
        margin: 0;
    }

    &__link {
        flex-shrink: 0;
        margin-left: 30px;

        @include breakpoint(medium down) {
            margin: 28px 0 0;
        }
    }

    &__close {
        @include trs;
        @include size(40px);
        align-items: center;
        background: none;
        border: 0;
        border-radius: 4px;
        color: $color-white;
        cursor: pointer;
        display: flex;
        flex-shrink: 0;
        font-size: 3rem;
        justify-content: center;
        margin-left: 70px;
        position: relative;
        text-decoration: none;

        @include fa-icon-style {
            color: inherit;
        }

        @include breakpoint(medium down) {
            @include absolute(20px, 30px, null, null);
            margin: 0;
        }

        @include breakpoint(small down) {
            right: 10px;
            top: 30px;
        }

        @include on-event {
            background-color: $color-white;
            color: var(--color-1--1);
        }
    }

    .is-popup & {
        #{$this}__wrapper {
            align-items: flex-start;
        }

        #{$this}__image {
            margin-right: 30px;

            @include breakpoint(medium down) {
                margin-right: 13px;
                max-width: 110px;
            }

            @include breakpoint(small down) {
                margin: 0 auto 20px;
            }
        }

        #{$this}__title {
            font-size: 2.4rem;
            line-height: 2.8rem;
            margin: 0 0 15px;

            @include breakpoint(medium down) {
                font-size: 2.2rem;
                line-height: 2.4rem;
            }

            @include breakpoint(small down) {
                font-size: 1.8rem;
                line-height: 2.2rem;
                margin: 0 0 5px;
                text-align: center;
            }
        }

        #{$this}__teaser {
            font-size: 1.7rem;
            line-height: 2.4rem;

            @include breakpoint(small down) {
                font-size: 1.4rem;
                line-height: 1.8rem;
                text-align: center;
            }
        }

        #{$this}__link {
            margin: 32px 0 0;

            @include breakpoint(small down) {
                margin: 40px 0 0;
            }
        }

        #{$this}__close {
            @include absolute(20px, 20px, null, null);
            margin: 0;

            @include breakpoint(small down) {
                right: 10px;
                top: 30px;
            }
        }
    }
}
