.discover-content,
.discover-home {
    $this: &;

    &.is-full-width {
        #{$this} {
            &__content {
                @include full-width-block;
            }
        }
    }

    &__container {
        @extend %container;
    }

    & &__list-item {
        margin-bottom: 0;
    }
}

.discover-home {
    &__content {
        @include breakpoint(medium only) {
            padding: 0 22px;
        }
    }

    &__title {
        margin-bottom: 41px;

        @include breakpoint(medium down) {
            margin-bottom: 30px;
        }

        @include breakpoint(small down) {
            margin-bottom: 20px;
        }

        .title__content {
            @include breakpoint(small down) {
                margin-left: 40px;
            }
        }
    }

    &.is-full-width {
        .title {
            @include breakpoint(large only) {
                max-width: 100%;
                position: relative;
            }
        }
    }
}
