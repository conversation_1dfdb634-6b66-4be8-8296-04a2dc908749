.hero {
    &.is-type-5 {
        .hero-item {
            &__content {
                @extend %link-block-context;
                @extend %underline-context;
                @include trs;
                @include absolute(null, 40px, 251px, null);
                box-sizing: border-box;
                color: $color-white;
                display: block;
                max-width: 370px;
                overflow: hidden;
                text-align: right;
                text-decoration: none;
                z-index: 1;

                @include breakpoint(medium down) {
                    margin-bottom: -97px;
                    margin-left: 97px;
                    max-width: 100%;
                    order: 1;
                    padding-top: 5px;
                    position: static;
                    text-align: left;
                    transform: none;
                    width: 565px;
                }

                @include breakpoint(small down) {
                    bottom: 72px;
                    margin-bottom: -110px;
                    margin-left: 0;
                    padding: 4px 20px;
                    width: 100%;
                }

                &::before {
                    @include trs;
                    @include absolute(15px, 15px, 15px, 15px);
                    border: 1px solid rgba($color-white, 0.5);
                    content: "";
                    opacity: 0;
                    visibility: hidden;
                }
            }

            &__category {
                color: $color-black;
                margin-bottom: 5px;
                
                @include breakpoint(medium down) {
                    margin-bottom: 0;
                }

                &.theme {
                    letter-spacing: 0;
                    line-height: 22px;

                    @include breakpoint(medium down) {
                        font-size: 1.6rem;
                        line-height: 19px;
                    }

                    @include breakpoint(small down) {
                        font-size: 1.4rem;
                        line-height: 18px;
                    }
                }
            }

            &__title {
                @include font(null, 3.5rem, var(--fw-bold));
                color: $color-black;
                line-height: 43px;

                @include breakpoint(medium down) {
                    font-size: 3rem;
                    line-height: 37px;
                }

                @include breakpoint(small down) {
                    font-size: 2.3rem;
                    line-height: 28px;
                }

                @include on-event() {
                    text-decoration: underline;
                }
            }

            &__picture {
                &::after {
                    height: 705px;

                    @include breakpoint(medium down) {
                        height: 482px;
                    }
                }
            }
        }

        .hero-carousel {
            &__group {
                @include breakpoint(medium down) {
                    bottom: 154px;
                    left: 189px;
                    right: auto;
                }
            }
        }
    }
}
