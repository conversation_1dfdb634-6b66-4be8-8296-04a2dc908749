.news-focus {
    $this: &;

    &::before {
        @include absolute(340px, null, 0, 50%);
        @include size(100vw, auto);
        background-color: $color-3--2;
        // content: '';
        transform: translateX(-50%);

        @include breakpoint(medium down) {
            top: 325px;
        }

        @include breakpoint(small down) {
            content: none;
        }
    }

    &__wrapper {
        @extend %link-block-context;
        align-items: flex-start;
        display: flex;
        flex-direction: column-reverse;

        @include breakpoint(medium down) {
            flex-direction: column-reverse;
            margin-left: auto;
            width: fit-content;
        }

        @include on-event() {
            #{$this}__title-link {
                .underline {
                    background-size: 100% 100%;
                }
            }
        }
    }

    &__picture {
        @include size(792px, 594px);
        flex-shrink: 0;

        @include breakpoint(medium down) {
            @include size(563px, 422px);
        }

        @include breakpoint(small down) {
            @include size(320px, 240px);
        }

        img {
            @include object-fit();
            @include size(100% !important);
        }
    }

    &__content {
        @include line-decor(100px, 16px, "after");
        background-color: $color-white;
        margin-right: auto;
        margin-top: -56px;
        max-width: 524px;
        padding: 43px 110px 20px 69px;
        width: 100%;
        z-index: 2;

        @include breakpoint(medium down) {
            margin: 0 auto;
            max-width: 500px;
            min-height: auto;
            padding: 20px 0 0;
            position: static;
        }

        @include breakpoint(small down) {
            margin: 0;
            max-width: 100%;
            padding: 21px 54px 20px;
        }

        &::after {
            content: none;
        }
    }

    #{$this}__picture-link {
        overflow: hidden;
    }

    &__publication-date {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: 28px;
        margin-top: 10px;

        @include breakpoint(medium down) {
            font-size: 1.4rem;
            font-weight: var(--fw-medium);
            text-align: center;
        }

        @include breakpoint(small down) {
            margin-top: 5px;
        }
    }

    &__title {
        &.item-title {
            font-size: 3rem;
            letter-spacing: 0;
            line-height: 34px;
            max-width: 324px;

            @include breakpoint(medium down) {
                font-size: 2rem;
                line-height: 24px;
                max-width: 100%;
                text-align: center;
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        line-height: 16px;
        margin-bottom: 5px;
        margin-left: 15px;
        position: relative;

        @include breakpoint(medium down) {
            margin-bottom: 5px;
            margin-left: 15px;
        }

        @include breakpoint(small down) {
            margin-bottom: 10px;
        }

        &::before {
            @include absolute(0, null, null, -11px);
            @include size(2px, 16px);
            background-color: var(--color-2--1);
            content: "";
            rotate: 25deg;

            @include breakpoint(medium down) {
                left: 4px;
            }

            @include breakpoint(small down) {
                left: 4px;
            }
        }

        &.theme {
            font-size: 1.3rem;
            letter-spacing: 0;

            @include breakpoint(medium down) {
                font-size: 1.2rem;
                margin: 0 auto 5px;
                padding-left: 16px;
                text-align: center;
                width: fit-content;
            }

            @include breakpoint(small down) {
                padding-left: 15px;
            }
        }
    }

    + .news-list {
        position: relative;

        &::before {
            @include absolute(-80px, null, null, 50%);
            @include size(100vw, 275px);
            background-color: $color-3--2;
            content: "";
            transform: translateX(-50%);
            z-index: -1;

            @include breakpoint(medium down) {
                bottom: 100px;
                height: auto;
            }

            @include breakpoint(small down) {
                content: none;
            }
        }
    }
}
