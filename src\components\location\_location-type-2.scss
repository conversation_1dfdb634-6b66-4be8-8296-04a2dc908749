.location-map {
    $this: &;

    .is-type-2 & {
        position: static;
        transform: none;
        z-index: 1;

        @include breakpoint(medium down) {
            margin-inline: auto;
        }

        @include breakpoint(small down) {
            @include reset-position;
            margin-bottom: 0;
            transform: none;
        }

        #{$this}__figure {
            @include size(620px, 608px);

            @include breakpoint(medium down) {
                @include size(305px, 299px);
            }
        }

        svg {
            @include size(100%);
            position: relative;
        }

        #{$this}__region {
            position: relative;
            z-index: 20;

            path {
                @include trs;
                // fill: var(--color-1--1);
                // stroke: $color-white;
            }

            &[data-active],
            &:hover,
            &:focus {
                path {
                    fill: var(--color-2--1);
                }
            }
        }
    }
}

.location-description {
    .is-type-2 & {
        padding: 0;

        @include breakpoint(medium down) {
            align-items: flex-start;
            margin-top: 30px;
        }

        @include breakpoint(small down) {
            margin-top: 30px;
        }
    }
}
