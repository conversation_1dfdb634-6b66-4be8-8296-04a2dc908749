{%- from 'views/core-components/icon.njk' import Icon -%}
{% from 'views/utils/utils.njk' import setAttr with context %}
{%- from 'views/core-components/list.njk' import List -%}
{%- import 'views/core-components/form.njk' as Form -%}
{% from 'components/popup-close-btn/popup-close-btn.njk' import PopupCloseBtn %}
{%- from 'components/favorite/favorite.njk' import FavoritesSection -%}
{%- from 'components/share/share.njk' import Share -%}

{#
    SearchPopup template.
#}
{%- macro NotepadPopup() -%}
    <div class="popup notepad-popup" id="notepad-popup">
        <div class="notepad-popup__wrapper">
            <div class="table-responsive {{ modifier }}">
                <div class="favorites-table__header">
                    <div class="favorites-table__header-title">Mon bloc-note</div>
                    <div class="favorites-table__header-actions">
                        <input type="checkbox" id="checkAll"/>
                        <label for="checkAll">Tout sélectionner</label>
                    </div>
                </div>
                <table class="table favorites-table" id="favoritesTable">
                    <tbody></tbody>
                </table>

                <div class="actions">
                    <button id="btnDelete" class="btn delete-btn" style="display: none;">SUPPRIMER</button>
                    <!-- N8 -->
                    <button id="btnPrint" class="btn print-btn" style="display: none;">IMPRIMER</button>
                    <!-- N9 -->
                    <button 
                        id="btnSend" 
                        class="btn send-btn share-list__link is-e-mail iframe js-tooltip" 
                        style="display: none;"
                            aria-label="Partager cette page par e-mail - fenêtre modale"
                            data-content="Partager cette page par e-mail"
                            aria-haspopup="dialog"
                            data-dialog-label="Partager cette page par e-mail"
                            data-type="iframe"
                            data-fancybox
                            data-src="./test-form.html"
                            data-role="presentation"
                    >
                        ENVOYER
                    </button>
                </div>
            </div>

        </div>
        {{ PopupCloseBtn(
            modifier = 'btn is-only-icon is-inverted is-small is-outside',
            ghostText = true,
            tooltip = 'Fermer la recherche'
        ) }}
    </div>
{%- endmacro -%}

{#
    Search template.
#}
{%- macro Notepad(
    dialogLabelText = 'bloc note',
    ghostText = 'Ouvrir le bloc note'
) -%}
    <a 
        id="notepadBtn"
        href="#notepad-popup" 
        data-fancybox 
        data-small-btn="false" 
        data-toolbar="false" 
        data-content="Ouvrir le bloc note" 
        class="btn is-sm-small is-primary" 
        data-fancybox-body-class="is-notepad-opened" 
        aria-haspopup="dialog"
        data-dialog-label="{{ dialogLabelText }}"
    >
        {{ Icon('far fa-light fa-star') }}
        <span class="ghost">{{ ghostText }}</span>
        <span class="btn__text">Bloc note</span>
    </a>
    <div class="notepad__popup" hidden="hidden">
        {{ NotepadPopup() }}
    </div>
{%- endmacro -%}