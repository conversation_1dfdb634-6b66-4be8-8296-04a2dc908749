.infowidget {
    background: $color-3--2;
    border-radius: 5px;
    margin: 0 0 70px;
    min-height: 125px;
    overflow: hidden;
    padding: 40px 40px 45px 104px;
    position: relative;

    @include breakpoint(medium down) {
        margin: 0 auto 50px;
    }

    @include breakpoint(small down) {
        margin: 50px auto;
        padding: 65px 23px 30px 27px;
    }

    .rte {
        &:first-child {
            margin-top: 0 !important;
        }

        > *:last-child {
            margin-bottom: 0 !important;
        }

        li,
        p:not([class]) {
            a {
                color: $color-black;
                text-decoration: underline;

                @include on-event {
                    background-color: transparent;
                    color: $color-black;
                    text-decoration: none;
                }
            }
        }
    }

    &__svg {
        @include absolute(0, null, null, 0);
        @include size(64px, 100%);
        background-color: var(--color-2--1);
        display: flex;
        justify-content: flex-start;
        padding: 40px 11px 40px 15px;
        z-index: 2;

        @include breakpoint(small down) {
            @include size(100%, 45px);
            padding: 8px 30px;
        }

        svg {
            @include size(34px, 38px);
            display: block;
            fill: $color-black;

            @include breakpoint(small down) {
                @include size(22px, 26px);
            }
        }
    }
}
