.partners {
    background-color: $color-white;
    margin-bottom: 120px;

    @include breakpoint(medium down) {
        margin-bottom: 110px;
        padding: 40px 0;
    }

    @include breakpoint(small down) {
        padding: 0;
    }

    &__container {
        @extend %container;
        display: flex;

        @include breakpoint(medium down) {
            margin: 0 auto 0 62px;
            max-width: 606px;
            padding: 0;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            margin: 0 auto;
            max-width: 229px;
            padding: 0;
        }
    }

    &__title-container {
        margin-right: 25px;

        @include breakpoint(medium down) {
            margin-right: 17px;
        }
    }

    &__title {
        @include font(null, 2.4rem, var(--fw-bold));
        flex-shrink: 0;
        line-height: 28px;

        @include breakpoint(medium down) {
            font-size: 2rem;
            margin: 0;
        }

        @include breakpoint(small down) {
            text-align: center;
        }
    }

    &__content {
        @include breakpoint(medium down) {
            width: 360px;
        }

        @include breakpoint(small down) {
            margin-top: 34px;
            width: 229px;
        }
    }

    &__list {
        display: flex;
        flex-wrap: wrap;
        min-height: 120px;

        @include breakpoint(medium down) {
            justify-content: space-between;
            min-height: auto;
        }

        @include breakpoint(small down) {
            justify-content: center;
        }
    }

    &__item {
        margin-right: 34px;
        padding: 0;
        width: 157px;

        @include breakpoint(medium down) {
            margin-bottom: 34px;
            margin-right: 0;
        }

        &:last-of-type {
            margin-right: 0;

            @include breakpoint(medium down) {
                margin-bottom: 0;
            }
        }
    }

    &__link {
        align-items: center;
        background-color: $color-white;
        display: flex;
        justify-content: center;
        min-height: 100px;
    }

    &__item-title {
        @include font(var(--typo-1), 1.3rem, var(--fw-medium));
        line-height: 16px;
        margin-top: 15px;
    }

    &__image {
        @include size(157px, 99px);
        pointer-events: none;

        img {
            @include size(100%);
            background-color: transparent;
        }
    }

    &__more-partners {
        .btn {
            background-color: transparent;
            border: 1px solid var(--color-1--2);
            line-height: 15px;
            margin-top: 20px;
            min-height: auto;
            padding: 0;
            padding: 14px 20px;
            width: fit-content;

            span[class*="fa-"] {
                color: var(--color-1--2);
            }

            @include on-event() {
                background-color: var(--color-1--2);

                .btn__text,
                span[class*="fa-"] {
                    color: $color-white;
                }
            }

            &__text {
                @include font(var(--typo-1), 1.2rem, var(--fw-medium));
                color: var(--color-1--2);
            }

            span:not([class*="fa-"]) {
                letter-spacing: 0;
            }
        }
    }
}
