.profile-nav {
    $this: &;

    height: 100%;
    position: relative;

    &::before {
        @include size(1px, 38px);
        @include absolute(50%, 50%, null, null);
        background-color: $color-black;
        content: '';
        rotate: 21deg;
        transform: translateY(-50%);
    }

    &.is-open {
        .profile-nav {
            &::before {
                background-color: $color-white;
            }
        }
    }

    @include on-event() {
        &::before {
            background-color: $color-white;
        }
    }

    @include breakpoint(medium down) {
        order: -1;
    }

    &__item {
        color: $color-white;

        @include on-event() {
            #{$this}__link-text {
                text-decoration: underline;
            }
        }
    }

    &__toggle {
        @include icon-after($fa-var-chevron-down);
        @include trs;
        @include size(149px, 53px);
        align-content: center;
        align-items: center;
        background-color: transparent;
        border: 1px solid;
        display: flex;
        margin: 0;
        padding: 10px 23px;

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        @include breakpoint(medium down) {
            border: none;
            color: $color-white;
            height: 52px;
        }

        &::after {
            @include absolute(null, 30px, null, null);
        }

        &.is-open {
            background-color: var(--color-1--1);
            box-shadow: none;

            &::after {
                transform: scaleY(-1);
            }
        }
    }

    &__toggle-text {
        @include font(var(--typo-1), 1.5rem, var(--fw-bold));
        line-height: 20px;
        position: relative;
        text-align: left;
        text-transform: uppercase;
        width: 50%;

        // @include on-event() {
        //     &::before {
        //         background-color: $color-white;
        //     }
        // }
    }

    &__toggle-icon {
        @include icon-before($fa-var-chevron-down, $fw: var(--fw-normal));
        @include font(var(--typo-1), 1.6rem, var(--fw-bold));
        color: inherit;
        margin-right: 5px;

        &::before {
            color: inherit;
            // content: none;
        }
    }

    &__block {
        @include trs;
        background-color: var(--color-1--1);
        left: auto;
        padding: 20px 0 35px;
        right: 0;
        width: 235px;
        z-index: 10;

        @include breakpoint(medium down) {
            width: 255px;
        }
    }

    &__menu {
        @include font(var(--typo-1), 1.6rem);
        color: var(--color-1--2);
    }

    &__link {
        @include trs;
        align-items: flex-start;
        display: flex;
        line-height: 1;
        padding: 6.5px 40px;
        text-decoration: none;

        @include fa-icon-style(false) {
            color: $color-white;
            font-size: 1.2rem;
            font-weight: var(--fw-normal);
            margin: 2px 5px 0 0;
        }

        @include on-event {
            background-color: var(--color-1--1);
        }
    }

    &__link-text {
        flex-grow: 1;
    }

    .is-aside-nav & {
        height: auto;
        margin: 20px 20px 20px;

        @include breakpoint(medium down) {
            margin-left: 0;
        }

        @include breakpoint(small down) {
            margin-right: 0;
        }

        #{$this}__block {
            background-color: transparent;
            box-shadow: none;
            left: 0;
            min-width: auto;
            padding-bottom: 20px;
            padding-top: 0;
            position: relative;
            transition: none;
            width: 542px;
        }

        #{$this}__toggle {
            @include size(542px, 45px);
            background-color: transparent;
            border: 1px solid $color-white;
            color: $color-white;
            cursor: pointer;
            font-size: 1.8rem;
            height: auto;
            margin: 0;
            padding: 5px 0;

            @include icon-after($fa-var-chevron-down);

            @include on-event {
                border-color: $color-white;
            }

            @include breakpoint(medium down) {
                height: auto;
                margin: 0 0 20px;
            }

            &::after {
                @include trs;
                @include size(45px);
                align-items: center;
                background-color: $color-white;
                color: $color-black;
                display: flex;
                font-size: 1.6rem;
                justify-content: center;
                left: 0;
            }

            &::after,
            >* {
                padding: 0 3px;
            }
        }

        #{$this}__menu {
            border: 1px solid $color-white;
        }

        #{$this}__toggle-icon {
            font-size: 3rem;
        }

        #{$this}__link {
            color: $color-dark;
            font-size: 1.6rem;
            padding-left: 75px;

            @include fa-icon-style(false) {
                @include trs(color);
                color: $color-dark;
                margin-right: 8px;
            }

            @include on-event {
                background-color: transparent;
                text-decoration: underline;

                @include fa-icon-style(false) {
                    color: var(--color-1--1);
                }
            }
        }
    }

    .main-nav__profile & {
        #{$this} {
            &__toggle {
                @include size(542px, 45px);

                @include breakpoint(small down) {
                    width: 100%;
                }
            }

            &__toggle-text {
                font-size: 2.8rem;
                line-height: 27px;
                padding-left: 71px;

                @include breakpoint(small down) {
                    font-size: 2.2rem;
                    padding-left: 52px;
                    width: 100%;
                }

                &::before {
                    content: none;
                }
            }
        }
    }
}
