.js-map-popup-show,
.js-map-popup-hide,
.js-map-animate-marker {
    * {
        pointer-events: none;
    }
}

.map-popup {
    $this: &;

    background-color: $color-3--1;
    box-shadow: 0 0 16px rgba(0, 0, 0, 0.16);
    display: flex;
    flex-direction: column;
    height: 100%;
    pointer-events: auto;
    position: relative;

    &.is-hidden {
        display: none;
        pointer-events: none;
    }

    & &__close[class] {
        @include absolute(0, 0);
        background-color: var(--color-1--1);
        border: none;
        border-radius: 0;
        z-index: 5;

        @include fa-icon-style(false) {
            @include trs;
            pointer-events: none;
        }

        @include on-event {
            background-color: var(--color-2--2);
        }
    }

    &__content {
        flex-grow: 1;
        height: 1%;
    }

    &__image {
        display: block;

        @include breakpoint(small down) {
            max-height: 154px;
            overflow: hidden;

            img {
                width: 100%;
            }
        }

        + #{$this}__descr {
            padding: 30px 44px 0 30px;

            @include breakpoint(medium down) {
                padding: 37px 37px 0;
            }
        }
    }

    &__content-scroll {
        height: 100%;
        overflow-y: auto;
    }

    &__heading {
        background-color: var(--color-1--1);
        color: $color-white;
        display: flex;
        padding: 20px 50px 20px 20px;
    }

    &__heading-icon {
        color: inherit;
        font-size: 2rem;
        margin-right: 10px;

        @include icon-after($fa-var-map-marker);
    }

    &__heading-content {
        color: inherit;
        flex-grow: 1;
        width: 1%;
    }

    &__heading-category {
        @include font(var(--typo-1), 1rem, var(--fw-bold));
        color: inherit;
        text-transform: uppercase;
    }

    &__heading-title {
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        color: inherit;
    }

    &__descr {
        padding: 60px 37px 0;
    }

    &__content-wrap {
        @extend %underline-context;
        display: block;
    }

    &__data {
        padding: 0 44px 30px 30px;

        @include breakpoint(medium down) {
            padding: 0 37px 73px;
        }

        > *:first-child {
            margin-top: 0;
        }

        > *:last-child {
            margin-bottom: 0;
        }
    }

    &__date {
        margin: 20px 0;
    }

    &__category {
        margin-bottom: 10px;

        &.theme {
            font-size: 1.5rem;
            letter-spacing: 2.7px;
        }
    }

    &__title {
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        color: $color-black;
        line-height: 2.4rem;
        margin-bottom: 20px;
    }

    &__info {
        margin: 20px 0 20px;
    }

    &__actions {
        margin: 0 0 20px;

        > *:not(:last-child) {
            margin-bottom: 10px;
        }
    }

    &__event {
        margin-bottom: 5px;
        margin-left: -15px;
        margin-right: -15px;
        padding: 15px;
        position: relative;
        z-index: 2;

        &:first-of-type {
            margin-top: 20px;
        }

        &:last-of-type {
            margin-bottom: 30px;
        }
    }

    &__event-meta {
        align-items: center;
        border-bottom: 1px solid $color-black;
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;
        padding-bottom: 10px;
    }

    &__event-date {
        .date__item {
            &.is-day {
                margin-right: 3px;
            }
        }
    }

    &__event-hours {
        font-size: 1.4rem;
        line-height: 1;
        margin-top: 10px;
    }

    &__event-theme {
        font-size: 1.4rem;
        margin-bottom: 7px;
    }

    &__event-title {
        font-size: 1.7rem;
        line-height: 1.1;
    }

    &__event-link {
        text-decoration: none;

        &::before {
            @include size(100%);
            @include absolute(0, 0);
            content: '';
        }

        &::after {
            @include trs;
            @include size(100%);
            @include absolute(0, 0);
            background-color: rgba($color-3--2, 0.65);
            content: '';
            opacity: 0;
            z-index: -1;
        }

        @include on-event {
            text-decoration: underline;

            &::after {
                opacity: 1;
            }
        }
    }
}
