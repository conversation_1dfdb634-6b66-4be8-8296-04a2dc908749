{%- from 'views/core-components/infos.njk' import InfoBlock, InfoItem, CreateInfoItem -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/widget.njk' import Widget -%}
{%- from 'views/core-components/title.njk' import TitleRTE, TitleWidget -%}

{#
    ContactItem template.
#}
{%- macro ContactsItem(
    className = '',
    imageSizes = ['180x180'],
    category = true,
    contentInfo = true,
    structure = false,
    showImage = false
) -%}
    <article class="contact-item {{ 'is-structure' if structure }} {{ className }}">
        {% if showImage %}
            {{ Image({
        sizes: imageSizes,
        className: 'contact-item__picture',
        serviceID: range(50) | random,
        alt: ''
    }) }}
        {% endif %}
        <div class="contact-item__content">
            <div class="contact-item__content-top">
                <h3 class="contact-item__title">
                    {%- if category %}
                        <span class="contact-item__theme">Thématique</span>
                        <span class="sr-only">
                            :
                        </span>
                    {%- endif %}
                    <a href="single-{{ 'structures' if structure else 'contacts' }}.html" class="contact-item__title-link">
                        <span class="underline">
                            {{ 'Titre de la fiche lorem ipsum dolor' if structure else 'Prénomlorem Nomsitamet' }}
                        </span>
                    </a>
                </h3>
            </div>
            {% if contentInfo %}
                <div class="contact-item__content-info">
                    <div class="contact-item__details">
                        {% if structure %}
                            {% call InfoBlock() %}
                            {{ InfoItem(type = 'address') }}
                            {{ InfoItem(type = 'hours') }}
                            {% endcall %}
                            {% call InfoBlock() %}
                            {{ InfoItem(type = 'website') }}
                            {% endcall %}
                        {% else %}
                            <p class="contact-item__function is-main">Fonction 1 lorem ipsum dolor sir amet</p>
                            <p class="contact-item__function">Fonction 2 lorem ipsum dolor consectur elis</p>
                            <p class="contact-item__function">Fonction 3 passam filis poder</p>
                        {% endif %}
                    </div>
                    <ul class="contact-item__infos">
                        {% if structure %}
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0639987845',
                                    text = '06 39 98 78 45',
                                    textSrOnly = 'Mobile',
                                    className = 'btn is-small',
                                    icon = 'far fa-mobile'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>
                        {% else %}
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>
                        {% endif %}
                    </ul>
                </div>
            {% endif %}
        </div>
    </article>
{%- endmacro -%}

{#
    ContactList template.
#}
{%- macro ContactsList(
    itemClass = 'contact-list__item',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 6,
    showImage = true
) -%}
    {% call List(itemClass = itemClass, cols = cols, mdCols = mdCols, smCols = smCols, xsCols = xsCols, count = count) %}
    {{ ContactsItem(showImage = showImage) }}
    {% endcall %}
{%- endmacro -%}

{#
    Structures template.
#}
{%- macro StructuresList(
    itemClass = 'has-mb-1',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 6,
     showImage = true
) -%}
    {% call List(itemClass = itemClass, cols = cols, mdCols = mdCols, smCols = smCols, xsCols = xsCols, count = count) %}
    {{ ContactsItem(imageSizes = ['250x166'], structure = true) }}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsContent template.
#}
{%- macro ContactsContent(
    className = 'contact-content',
    titleText = 'Contact',
    itemsCount = 1,
    proposerButton = false,
    moreButton = false
) -%}
    {% call Section(className = className, container = false) %}
    <div class="section__title">
        {{ TitleRTE(
                text = titleText,
                iconPath = false
            ) }}
    </div>
    <div class="section__content">
        {{ ContactsList(
                itemClass = '',
                count = itemsCount,
                cols = 1,
                mdCols = false,
                smCols = 1,
                xsCols = false,
                showImage = false
            ) }}
    </div>
    {% if moreButton or proposerButton %}
        <div class="section__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un contact',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les contacts',
                        className = 'btn',
                        icon = 'far fa-plus-circle'
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsStructuresContent template.
#}
{%- macro ContactsStructuresContent(
    className = 'contact-section',
    titleText = 'Structure',
    itemsCount = 1,
    proposerButton = false,
    moreButton = false,
    showImage = true
) -%}
    {% call Section(className = className, container = false) %}
    <div class="section__title">
        {{ TitleRTE(
                text = titleText
            ) }}
    </div>
    <div class="section__content">
        {% call List(count = itemsCount, cols = 1, mdCols = false, smCols = false, xsCols = false) %}
        {{ ContactsItem(imageSizes = ['250x166'], structure = true, showImage = true) }}
        {% endcall %}
    </div>
    {% if moreButton or proposerButton %}
        <div class="section__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = 'page-proposer.html',
                        text = 'Proposer un structure',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        href = 'list-contacts.html',
                        text = 'Toutes les structures',
                        className = 'btn',
                        icon = 'far fa-plus-circle'
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsSidebar template.
#}
{%- macro ContactsSidebar(
    titleText = 'Contact',
    itemsCount = 1,
    moreButton = false,
    proposerButton = false
) -%}
    {% call Widget(className = 'contact-widget') %}
    <div class="widget__title">
        {{ TitleWidget(
                className = 'is-center',
                text = titleText,
                iconPath = false
            ) }}
    </div>
    <div class="widget__content">
        {{ ContactsList(
                itemClass = '',
                count = itemsCount,
                cols = 1,
                mdCols = false,
                smCols = false,
                xsCols = false,
                showImage = false
            ) }}
    </div>
    {% if moreButton or proposerButton %}
        <div class="widget__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un contact',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les contacts',
                        className = 'btn',
                        icon = 'far fa-plus-circle'
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsStructuresSidebar template.
#}
{%- macro ContactsStructuresSidebar(
    titleText = 'Structure',
    itemsCount = 1,
    moreButton = false,
    proposerButton = false
) -%}
    {% call Widget(className = 'contact-widget') %}
    <div class="widget__title">
        {{ TitleWidget(
                className = 'is-center',
                text = titleText,
                iconPath = false
            ) }}
    </div>
    <div class="widget__content">
        {% call List(count = itemsCount, cols = 1, mdCols = false, smCols = false, xsCols = false) %}
        {{ ContactsItem({ imageSizes: ['250x200'], structure: true }) }}
        {% endcall %}
    </div>
    {% if moreButton or proposerButton %}
        <div class="widget__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un contact',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les contacts',
                        className = 'btn',
                        icon = 'far fa-plus-circle'
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}