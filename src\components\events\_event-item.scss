.event-item {
    $this: &;

    @extend %link-block-context;
    cursor: pointer;
    display: flex;
    flex-direction: column-reverse;
    margin: 0 auto;
    max-width: 384px;
    position: relative;

    @include breakpoint(medium down) {
        max-width: 100%;
    }

    @include breakpoint(small down) {
        align-items: flex-start;
        flex-direction: column-reverse;
    }

    // styles for HP with background
    .events-home & {
        #{$this} {
            &__content {
                @include breakpoint(large only) {
                    padding: 15px 30px 15px 0;
                }
            }

            &__title {
                .underline {
                    @include multiline-underline();
                }
            }

            &__category {
                color: var(--color-2--1);
            }

            &__time-place {
                .time-place__item {
                    color: var(--color-1--3);
                }
            }
        }
    }

    // styles for widget/sidebar on CP
    .events-widget & {
        #{$this} {
            &__date {
                @include breakpoint(large only) {
                    @include absolute(0, null, null, 0);
                    margin: 0;
                }
            }

            &__content {
                @include breakpoint(large only) {
                    padding: 30px 20px 10px 0;
                    text-align: left;
                }
            }

            &__time-place {
                .time-place__item {
                    @include breakpoint(large only) {
                        justify-content: flex-start;
                    }
                }
            }
        }
    }

    &__image {
        display: block;

        @include breakpoint(medium down) {
            @include size(100px, 67px);
        }

        @include breakpoint(small down) {
            @include min-size(100px, 67px);
            @include size(100px, 67px);
        }

        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__wrap {
        display: flex;
        flex-direction: column-reverse;
        width: 100%;
        z-index: 2;

        @include breakpoint(medium only) {
            align-items: center;
            flex-direction: row-reverse;
            justify-content: flex-end;
        }
    }

    &__date {
        margin-top: -49px;
        max-width: 270px;

        @include breakpoint(medium down) {
            @include absolute(0, 0, null, null);
            margin: 0;
            max-width: none;
            padding: 12px 20px;
            width: calc(100% - 100px);
        }

        @include breakpoint(small down) {
            margin: 0 0 0 100px;
            min-width: calc(100% - 100px);
        }

        &::before {
            content: none;
        }

        &__icon {
            color: var(--color-2--1);

            @include breakpoint(large only) {
                margin-left: 8px;
                margin-right: 0;
            }
        }
    }

    &__content {
        padding: 20px 30px 20px 0;

        @include breakpoint(medium down) {
            padding: 15px 0 0;
        }

        @include breakpoint(small down) {
            padding: 10px 0;
        }
    }

    &__title {
        line-height: 1.2;

        @include breakpoint(medium down) {
            font-size: 2rem;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        @include font(null, 1.2rem, var(--fw-medium));
        color: var(--color-1--1);
        margin: 5px 0;
    }

    &__time-place {
        margin-top: 15px;

        @include breakpoint(small down) {
            margin-top: 5px;
        }
    }

    &__actions {
        @include absolute(-5px, -5px, null, null);
        z-index: 11;
    }
}
