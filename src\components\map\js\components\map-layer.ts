import merge from 'deepmerge';

import {
    Tile as TileLayer,
    Vector as VectorLayer,
 } from 'ol/layer';

import {
    Cluster,
    OSM,
    Vector as VectorSource,
    BingMaps,
    WMTS,
} from 'ol/source';

import {
    Circle as CircleStyle,
    Icon,
    Fill,
    Stroke,
    Style,
    Text,
} from 'ol/style';

import WMTSTileGrid from 'ol/tilegrid/WMTS';
import Feature from 'ol/Feature';

import {
    KML,
    GeoJSON,
    GPX,
} from 'ol/format';

import { getWidth } from 'ol/extent';
import { get as getProjection } from 'ol/proj';

import { extendDefaults } from '@core/utils/object.utils';
import { getBrowserName } from '@core/utils/env.utils';

import StratisMapComponent from './map-component.abstract';
import { IStratisMapLayer } from '../models/map.model';
import { MapStylesTypes, LayerTypes } from '../enums/map.enum';
import { layerReadyCallback } from '../types/map.types';
import Point from 'ol/geom/Point';

/**
 * Wrapper layer for ol/layer
 */
export default class StratisMapLayer extends StratisMapComponent {
    protected options: IStratisMapLayer = {
        type: 'OSM',
        styleProps: {
            // POI default icons
            defaultMarker: 'images/map/map-marker.svg',
            selectedMarker: 'images/map/map-marker.svg',
            markerColor: '#4269e2',
            selectedMarkerColor: '#ff8977',
            // Cluster styles,
            cluster: {
                outerColor: '#4269e2',
                innerColor: '#4269e2',
                radius: 25,
                text: '',
                font: 'normal 16px Montserrat',
                fontColor: '#fff',
                strokeWidth: 0,
            },
            // Add circle with borders for selected POI
            selectedMarkerBorder: {
                color: 'rgba(255, 137, 119, 0.5)',
                radius: 40,
                width: 8,
            },
            // Add route styles
            route: {
                color: 'rgba(202, 0, 10, 0.2)',
                width: 4,
            },
        },
    };

    private isReady = false;
    private preview: HTMLImageElement | null = null;

    // Cache for markers style
    private styleCache = {};

    // Features style handlersss
    private readonly $featuresStyleHandler = this.featuresStyleHandler.bind(this);

    public static createFromHTML(element: HTMLElement): StratisMapLayer | null {
        try {
            const options = StratisMapLayer.createOptionsFromHTML(element) as IStratisMapLayer;
            return new StratisMapLayer(options);
        } catch (err) {
            console.error((err as any).message);
            return null;
        }
    }

    /**
     * Create StratisMapLayer instance.
     * @param {object} options - layer properties
     */
    public constructor(options: Partial<IStratisMapLayer> = {}) {
        super();

        this.options = extendDefaults(this.options, options);
        this.mergeStyleProps(options);
        this.createComponent();
    }

    /**
     * Get layer preview image.
     */
    public getPreview(): HTMLImageElement | null {
        return this.preview;
    }

    /**
     * Set preview image for layer.
     */
    public setPreview(element: HTMLImageElement): HTMLImageElement {
        this.preview = element;
        return this.preview;
    }

    /**
     * Get Openlayers source.
     */
    public getSource(): any {
        return this.component.getSource();
    }

    /**
     * Clear source.
     */
    public clearSource(): void {
        const source = this.getSource();

        if (source) {
            source.clear();
        }
    }

    /**
     * Update source with new features.
     * @param features
     */
    public updateSource(features: Feature<Point>[]): void {
        const source = this.getSource();

        source.clear();
        source.addFeatures(features);
        source.refresh();
    }

    /**
     * Get ready state.
     */
    public getReady(): boolean {
        return this.isReady;
    }

    /**
     * Get layer instance.
     */
    public getOlLayer(): any {
        return this.component;
    }

     /**
     * Make sure that layer is loaded.
     */
     public whenReady(cb: layerReadyCallback = () => {/** */}): void {
         const source = this.component.getSource();

         if (source) {
             const uid = this.component.get('uid');

             // TODO: generate layer preview .
             // if (this.component instanceof TileLayer) {
             //     source.on('tileloadend', mapEvent => {
             //         if (!this.getPreview()) {
             //             this.setPreview(mapEvent.tile.getImage());
             //         }
             //     });
             // }

             this.handleSourceChange(source, uid, this.component, cb);
         }
     }

    protected handleSourceChange(source, sourceUid, component, cb): void {
        let featureLoadEndFired = false;

        source.on('featuresloadend', (_: Event) => {
            if (source.getState() === 'ready') {
                const features: Feature<Point>[] = [];
                this.isReady = true;

                if (source instanceof Cluster) {
                    features.push(...source.getSource().getFeatures());
                }

                if (source instanceof VectorSource) {
                    const featuresWithUid = [...source.getFeatures()].map(feature => {
                        const isUidOnFEatureExist = feature.get('uid');
                        const isInteractionDisabled = component.get('disableInteractions');

                        feature.set('isInteractionDisabled', isInteractionDisabled);
                        if (!isUidOnFEatureExist) {
                            feature.set('uid', sourceUid);
                        }
                        return feature;
                    });

                    features.push(...featuresWithUid);
                }

                featureLoadEndFired = true;

                cb(component, source, features);
            }
        });

        source.on('change', (_: Event) => {
            if (featureLoadEndFired) {
                return;
            }

            const features: Feature<Point>[] = [];

            if (source.getState() === 'ready') {
                if (source instanceof Cluster) {
                    features.push(...source.getSource().getFeatures());
                }


                cb(this.component, source, features);
            }
        });
    }

    /**
     * Create layer.
     */
    protected createComponent(): void {
        switch (this.options.type.toLowerCase()) {
            case LayerTypes.bing:
                this.component = new TileLayer({
                    preload: Infinity,
                    visible: Boolean(this.options.isVisible),
                    source: new BingMaps(this.options as any),
                });
                break;
            case LayerTypes.cluster:
                this.createCluster(this.options);
                break;
            case LayerTypes.geojson:
                this.createGeoJSON(this.options);
                break;
            case LayerTypes.kml:
                this.createKML(this.options);
                break;
            case LayerTypes.geoportal:
                this.createGeoportal(this.options);
                break;
            case LayerTypes.gpx:
                this.createGPX(this.options);
                break;
            default:
                this.component = new TileLayer({
                    visible: Boolean(this.options.isVisible),
                    source: new OSM(this.options as any),
                });
        }


        if (this.options && this.options.attributions) {
            this.setCustomAttributions({ ...this.options });
        }

        // Convert all dataset properties to layer properties.
        StratisMapLayer.optionsToProps(this.component, this.options);
    }

    // Set custom attributions to BingMaps
    private setCustomAttributions(options): void {
        setTimeout(() => {
            const source = this.component.getSource();
            source.setAttributions(options.attributions);
            source.attributionsCollapsible_ = false;
        }, 500);
    }

    /**
     * Add custom styles from HTML to layer
     * @param options - custom options
     */
    private mergeStyleProps(options): void {
        const { customStyleProps } = options;

        if (customStyleProps) {
            this.options.styleProps = merge(this.options.styleProps, JSON.parse(customStyleProps)) as any;
        }
    }

    /**
     * Create styles for Cluster
     * @returns {Style[]} - array of ol.Style objects
     */
    private createClusterStyle(text = ''): Style[] {
        const {
            outerColor,
            innerColor,
            radius,
            strokeWidth,
            text: userText,
            font,
            fontColor,
        } = this.options.styleProps.cluster!;

        return [
            new Style({
                image: new CircleStyle({
                    radius: radius + 3,
                    stroke: new Stroke({
                        color: outerColor,
                        width: strokeWidth,
                    }),
                }),
            }),
            new Style({
                image: new CircleStyle({
                    fill: new Fill({
                        color: innerColor,
                    }),
                    radius,
                }),
                text: new Text({
                    text: userText || text,
                    fill: new Fill({
                        color: fontColor,
                    }),
                    font,
                }),
            }),
        ];
    }

    /**
     * Create styles for Marker
     * @param props {Object} - marker properties
     * @returns {Style} - ol.Style object
     */
    private createMarkerStyle(props): Style[] {
        const isIECondition = props?.icon?.indexOf('.svg') >= 0 && getBrowserName() === 'ie';
        const style: Style[] = [];
        const { selectedMarkerBorder, markerColor, selectedMarkerColor } = this.options.styleProps;

        let styleProp = props.selected ? 'selectedMarker' : 'defaultMarker';
        let color = props.markerColor || markerColor;

        if (props.icon && !props.selected && !isIECondition) {
            styleProp = props.icon;
        }

        if (props.selected && props.iconSelected && !isIECondition) {
            styleProp = props.iconSelected;
        }

        const src = this.options.styleProps[styleProp] || styleProp;

        if (props.selected && selectedMarkerBorder && typeof selectedMarkerBorder === 'object') {
            style.push(
                new Style({
                    image: new CircleStyle({
                        displacement: [0, 28],
                        stroke: new Stroke({
                            color: props.borderColor || selectedMarkerBorder.color,
                            width: props.borderWidth || selectedMarkerBorder.width,
                        }),
                        radius: props.borderRadius || selectedMarkerBorder.radius,
                    }),
                })
            );
        }

        if (props.selected) {
            color = props.markerColor || selectedMarkerColor || markerColor;
        }

        style.push(
            new Style({
                image: new Icon({
                    anchor: [0.5, 1],
                    crossOrigin: 'anonymous',
                    color,
                    imgSize: [43, 55],
                    src,
                }),
            })
        );

        return style;
    }

    /**
     * Create styles for map routes.
     * @private
     */
    private createRouteStyle(): Style[] {
        const color = this.options.styleProps.route?.color;
        const width = this.options.styleProps.route?.width;

        return [
            new Style({
                stroke: new Stroke({
                    color: color || '#3399CC',
                    width: width || 2,
                }),
            }),
        ];
    }

    /**
     * Use cache to process styles.
     * @private
     */
    private useStyleCache(cacheUid: string, stylesFor: MapStylesTypes, props?: any): Style[] {
        if (this.styleCache[cacheUid]) {
            return this.styleCache[cacheUid];
        }

        this.styleCache[cacheUid] = stylesFor === MapStylesTypes.cluster
            ? this.createClusterStyle(cacheUid)
            : this.createMarkerStyle(props);

        return this.styleCache[cacheUid];
    }

    /**
     * Style function when loading features.
     * @returns {*} - style
     */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    private featuresStyleHandler(feature: Feature<Point>): Style[] {
        const features = feature.get('features');
        const size = String(features?.length || 1);
        const markers: Feature<Point>[] = [];

        if (features) {
            features.forEach((singleFeature: any) => !singleFeature.get('hidden') && markers.push(singleFeature));
        }

        if (markers.length > 1) {
            return this.useStyleCache(size, MapStylesTypes.cluster);
        }

        const { defaultMarker, selectedMarker } = this.options;
        const [marker] = markers;
        const props = {
            uid: marker.get('uid'),
            icon: marker.get('icon'),
            markerColor: marker.get('markerColor'),
            iconSelected: marker.get('iconSelected'),
            selected: marker.get('selected'),
            borderColor: marker.get('borderColor'),
            borderWidth: marker.get('borderWidth'),
            borderRadius: marker.get('borderRadius'),
        };

        let styleProp;

        if (!props.selected) {
            styleProp = props.icon ? `marker-${props.uid}` : 'defaultMarker';
        } else {
            styleProp = props.iconSelected ? `marker-${props.uid}-selected` : 'selectedMarker';
        }

        props.icon = props.icon || defaultMarker;
        props.iconSelected = props.iconSelected || selectedMarker;

        return this.useStyleCache(styleProp, MapStylesTypes.marker, props);
    }

    /**
     * Create geoportal layer
     * @private
     */
    private createGeoportal(options: any = {}): void {
        const resolutions: number[] = [];
        const matrixIds: string[] = [];
        const proj3857 = getProjection('EPSG:3857');
        const maxResolution = getWidth(proj3857.getExtent()) / 256;

        for (let i = 0; i < 20; i++) {
            matrixIds[i] = i.toString();
            resolutions[i] = maxResolution / Math.pow(2, i);
        }

        this.component = new TileLayer({
            visible: Boolean(options.isVisible),
            source: new WMTS({
                ...options,
                url: options.url.replace('[key]', this.options.key),
                tileGrid: new WMTSTileGrid({
                    origin: [-20037508, 20037508],
                    resolutions,
                    matrixIds,
                }),
            }),
        });
    }


    /**
     * Create Cluster layer.
     * @param {object} options - layer settings
     */
    private createCluster(options: any = {}): void {
        this.component = new VectorLayer({
            source: new Cluster({
                distance: 40,
                source: new VectorSource({
                    features: options.features || null,
                }),
            }),
            style: options.onStyle || this.$featuresStyleHandler,
        });
    }

    /**
     * Create GeoJSON layer.
     * @param {object} options - layer settings
     */
    private createGeoJSON(options: any = {}): void {
        const layerSource = new VectorSource({
            format: new GeoJSON(),
        });

        if (options.url) {
            layerSource.setUrl(options.url);
        }

        if (options.routeGeometry && !options.url) {
            layerSource.addFeatures(
                new GeoJSON().readFeatures(
                    options.routeGeometry,
                    {
                        featureProjection: 'EPSG:3857',
                    }
                )
            );
        }

        this.component = new VectorLayer({
            source: layerSource,
            style: this.createRouteStyle.bind(this),
        });
    }

    /**
     * Create KML layer.
     * @param {object} options - layer settings
     */
    private createKML(options: any = {}): void {
        this.component = new VectorLayer({
            source: new VectorSource({
                format: new KML(),
                url: options.url,
            }),
        });
    }

    /**
     * Create GPX layer.
     * @param {object} options - layer settings
     */
    private createGPX(options: any = {}): void {
        this.component = new VectorLayer({
            source: new VectorSource({
                format: new GPX(),
                url: options.url,
            }),
        });
    }
}
