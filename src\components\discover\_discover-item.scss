.discover-item {
    $this: &;

    display: flex;

    @include breakpoint(small down) {
        flex-direction: column-reverse;
    }

    &.is-image-left {
        @include breakpoint(medium) {
            flex-direction: row-reverse;
        }
    }

    .is-full-width & {
        #{$this} {
            &__content-wrapper {
                @include breakpoint(small down) {
                    margin: 0;
                    width: 100%;
                }

                &::before {
                    content: none;
                }
            }

            &__content {
                margin: 0;
            }

            &__category {
                @include breakpoint(large only) {
                    margin-bottom: 20px;
                }
            }

            &__link {
                @include breakpoint(large only) {
                    @include min-size(100px, 63px);
                    font-size: 1.3rem;
                    font-weight: var(--fw-medium);
                    min-height: auto;
                    padding: 14px 30px;
                }
            }
        }
    }

    &__image {
        width: 50%;

        @include breakpoint(small down) {
            width: 100%;
        }

        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__content-wrapper {
        align-items: center;
        background-color: var(--color-1--2);
        display: flex;
        justify-content: center;
        padding: 139px 100px 138px 100px;
        position: relative;
        width: 50%;
        z-index: 2;

        @include breakpoint(medium down) {
            padding: 40px;
        }

        @include breakpoint(small down) {
            padding: 53px 20px 48px 21px;
            width: 100%;
        }
    }

    &__content {
        margin: 30px 42px;
        max-width: 502px;

        @include breakpoint(small down) {
            margin: 35px 42px;
            text-align: center;
        }
    }

    &__category {
        color: $color-white;
        font-size: 1.8rem;
        margin-bottom: 15px;

        @include breakpoint(medium down) {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        @include breakpoint(small down) {
            margin-bottom: 5px;
        }

        &.theme {
            letter-spacing: 0;
        }
    }

    &__title {
        color: $color-white;
        font-size: 3rem;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
        }

        @include on-event() {
            text-decoration: underline;
        }
    }

    & &__link {
        background-color: var(--color-2--1);
        color: var(--color-1--2);
        margin-top: 24px;

        @include breakpoint(medium down) {
            margin-top: 15px;
        }

        @include breakpoint(small down) {
            margin-top: 20px;
        }

        @include on-event() {
            background-color: var(--color-2--1);
            border-color: $color-white;
            color: $color-black;
        }
    }

    .page-content & {
        #{$this} {
            &__category {
                &.theme {
                    letter-spacing: 3.24px;

                    @include breakpoint(medium down) {
                        letter-spacing: 0;
                    }
                }

                @include breakpoint(medium down) {
                    font-size: 1.2rem;
                    margin-bottom: 5px;
                }
            }

            &__link {
                @include breakpoint(medium down) {
                    margin-top: 20px;
                }
            }

            &__content {
                @include breakpoint(small down) {
                    margin: 0;
                }
            }

            &__content-wrapper {
                @include breakpoint(small down) {
                    margin: -40px 20px 20px;
                    padding: 37px 27px;
                    width: calc(100% - 40px);
                }
            }

            &__title {
                &.item-title {
                    font-size: 1.6rem;
                    line-height: 24px;
                }
            }
        }
    }
}
