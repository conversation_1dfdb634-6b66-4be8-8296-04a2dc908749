{%- from 'views/utils/constants.njk' import kMainNav, kMainNavBottom -%}
{%- from 'views/utils/utils.njk' import svg -%}
{%- from 'components/logo/logo.njk' import Logo -%}
{%- from 'components/lang/lang.njk' import Lang -%}
{%- from 'components/google-translate/google-translate.njk' import GoogleTranslate -%}
{%- from 'components/page-image/page-image.njk' import PageImage -%}
{%- from 'components/profile-nav/profile-nav.njk' import ProfileNav -%}
{%- from 'components/search/search.njk' import Search -%}
{%- from 'components/main-nav-aside/main-nav-aside.njk' import MainNavAside -%}
{%- from 'components/main-nav-default/main-nav-default.njk' import MainNavDefault -%}
{%- from 'components/main-nav-toggle/main-nav-toggle.njk' import MainNavToggle -%}
{%- import 'components/heading/heading.njk' as Heading -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'components/notepad/notepad.njk' import Notepad -%}

{#
    HeaderNav template
#}
{%- macro HeaderNav(items = kMainNav) -%}
    <nav class="header-nav" role="navigation" aria-label="Navigation principale">
        <ul class="header-nav__list js-nav-links">
            {% for item in items %}
                <li class="header-nav__item {{ 'has-dropdown' if item.dropdown }}">
                    {% if item.dropdown %}
                        <button type="button" class="header-nav__link" data-index="{{ loop.index }}" aria-haspopup="dialog" aria-label="{{ item.text }} (fenêtre modale)">
                            {{ item.text }}
                        </button>
                    {% else %}
                        <a class="header-nav__link" href="#">{{ item.text }}</a>
                    {% endif %}
                </li>
            {% endfor %}
        </ul>
    </nav>
{%- endmacro -%}

{#
    Header template.
    @param {boolean} isHome - check if is home page
    @param {string} menuType - menu type (aside|mix|default|bottom)
    @param {string|boolean} usePageImage - false or pageImage path
    @param {boolean} useLogoCenter - center logo on home page
#}
{% macro Header(isHome = false, menuType = 'bottom', usePageImage = '', useSecondaryHeading = false, useLogoCenter = false) %}
    <header class="header-wrapper" role="banner">
        <div class="header-wrapper__container">
            <div class="header js-fixed-element {{ 'has-nav-bottom' if menuType === 'bottom' }} {{ 'has-logo-center' if useLogoCenter and isHome }}">
                <div class="subheader">
                    {{ Notepad ()}}
                    {# {{ Link(
                        href = "#",
                        text = 'Bloc note ',
                        className = 'btn is-sm-small is-primary',
                        icon = "far fa-light fa-star"
                    ) }} #}
                    {{ Link(
                        href = "#",
                        text = 'Mon espace ',
                        className = 'btn is-sm-small is-primary',
                        icon = "far fa-light fa-circle-user"
                    ) }}
                </div>
                <div class="header__inner">
                    <div class="header__logo">
                        {{ Logo(isHome) }}
                    </div>
                    <div class="header__components">
                        {% if menuType === 'default' or menuType === 'mix' %}
                            <div class="header__nav">
                                {% if menuType === 'default' %}
                                    {{ MainNavDefault(usePageImage) }}
                                {% elseif menuType === 'mix' %}
                                    {{ HeaderNav() }}
                                {% endif %}
                            </div>
                        {% endif %}
                        {% if menuType === 'bottom' %}
                            <div class="header__nav">
                                {{ HeaderNav(items = kMainNavBottom) }}
                            </div>
                        {% endif %}
                        <div class="header__profile">
                            {{ ProfileNav() }}
                        </div>
                        <div class="header__search" role="search">
                            {{ Search( dialogLabelText = 'Formulaire de recherche', ghostText = 'Ouvrir la recherche (fenêtre modale)') }}
                        </div>
                        <nav class="header__nav-toggle {{ 'is-lg-hide' if menuType !== 'aside' }}" role="navigation" aria-label="Navigation principale">
                            {{ MainNavToggle() }}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        {% if menuType !== 'default' %}
            <div class="header-wrapper__nav is-aside-nav">
                {{ MainNavAside(items = kMainNavBottom if menuType === 'bottom' else kMainNav, usePageImage) }}
            </div>
        {% endif %}
    </header>
    {%- if usePageImage and not useSecondaryHeading -%}
        {{ PageImage(usePageImage) }}
    {%- endif -%}
    {%- if useSecondaryHeading -%}
        {%- if usePageImage -%}
            {{ PageImage(usePageImage, imageSizes = ['480x379?479', '768x606?767', '1280x1010?1279', '1171x924']) }}
        {%- endif -%}
    {%- endif -%}
{% endmacro %}