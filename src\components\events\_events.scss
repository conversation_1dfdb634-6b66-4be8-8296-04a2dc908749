.events-home {
    overflow: hidden;
    padding: 0;
    position: relative;

    &.section {
        margin-bottom: 0;
    }

    .section__title {
        margin-bottom: 44px;

        @include breakpoint(medium down) {
            margin-bottom: 30px;
        }

        @include breakpoint(small down) {
            align-items: flex-start;
            margin-left: 18px;
        }

        .title__content {
            @include breakpoint(medium down) {
                margin-left: 0;
                padding-left: 0;
            }

            @include breakpoint(small down) {
                padding-left: -20px;
            }
        }
    }

    &__container {
        @extend %container;

        @include breakpoint(medium down) {
            max-width: 1130px;
            padding: 0 40px;
        }

        @include breakpoint(small down) {
            padding: 0;
        }
    }

    & &__content {
        position: relative;

        @include breakpoint(medium only) {
            padding: 0;
        }

        &:has(.events-home-block) {
            .events-home__list {
                @include breakpoint(large only) {
                    min-height: 694px;
                }
            }
        }

        .events-home-block {
            &:only-child {
                position: relative;
            }
        }
    }

    &__item {
        &:not(.swiper-slide-active) {
            .event-focus__content {
                display: none;
            }
        }
    }

    & &__list {
        display: grid;
        grid-column-gap: 0;
        grid-row-gap: 0;
        grid-template-columns: repeat(3, 1fr);
        position: relative;

        @include breakpoint(large only) {
            :nth-child(1) {
                grid-area: 1 / 1 / 2 / 2;
            }

            :nth-child(2) {
                grid-area: 2 / 1 / 3 / 2;
                margin-bottom: 10px;
            }

            :nth-child(3) {
                grid-area: 3 / 1 / 4 / 2;
            }

            :nth-child(4) {
                grid-area: 3 / 2 / 4 / 3;
            }

            :nth-child(5) {
                grid-area: 3 / 3 / 4 / 4;
            }
        }

        @include breakpoint(medium down) {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start !important;
            margin: 0;
        }

        @include breakpoint(small down) {
            margin: 31px auto 0;
            width: 320px;
        }
    }

    & &__list-item {
        height: fit-content;
        margin-bottom: 30px;

        @include breakpoint(medium down) {
            margin-bottom: 35px;
            padding: 0;
            width: calc(50% - 14px) !important;

            &:last-of-type {
                margin-bottom: 0;
            }
        }

        @include breakpoint(small down) {
            width: 100% !important;
        }

        &:nth-of-type(odd) {
            @include breakpoint(medium only) {
                margin-right: 14px;
            }
        }
    }

    & &__more-links {
        justify-content: flex-end;
        margin-top: 0;

        .btn {
            background-color: transparent;
            border-color: var(--color-1--2);
            color: var(--color-1--2);
            font-size: 1.2rem;
            margin: 0;
            min-height: auto;
            padding: 14px 30px;

            @include on-event() {
                background-color: $color-black;
                color: $color-white;
            }
        }

        @include breakpoint(small down) {
            margin: 30px 20px 0 auto;
            position: static;
        }
    }

    & &__eventlinks,
    & &__banner {
        margin: 70px 0;

        @include breakpoint(medium down) {
            margin: 60px 0 0;
        }
    }
}

.events-home-block {
    @include absolute(null, 0, null, null);
    @include size(792px, fit-content);

    @include breakpoint(medium down) {
        @include size(563px, 100%);
        margin-left: 0;
        position: relative;
    }

    @include breakpoint(small down) {
        margin: 0 auto;
        width: 320px;
    }

    &::before {
        @include size(371px, 353px);
        @include absolute(241px, -207px, null, null);
        background-image: image("motif-container.svg");
        background-repeat: no-repeat;
        content: "";

        @include breakpoint(medium down) {
            @include size(235px, 173px);
            background-size: cover;
            left: auto;
            right: -149px;
            top: 248px;
        }

        @include breakpoint(small down) {
            content: none;
        }
    }

    &__action {
        @include trs(opacity);
        background: none;
        border: 0;
        color: $color-black;
        cursor: pointer;
        display: inline-block;
        font-size: 1.5rem;
        padding: 0;

        @include on-event {
            opacity: 0.7;
        }

        &.js-is-playing {
            span {
                &::before {
                    @include size(15px !important);
                    margin-top: 1px;
                }
            }
        }

        &.is-active {
            color: var(--color-2--1);
            display: none;
        }

        span {
            pointer-events: none;

            &::before {
                font-weight: var(--fw-bold);
            }
        }
    }

    &__actions {
        display: flex;
        margin-left: 11px;

        @include breakpoint(medium down) {
            margin-left: 0;
        }
    }

    &__pagination {
        display: flex;

        .swiper-pagination {
            &__bullet {
                @include size(15px);
                border-color: $color-3--4;
                cursor: pointer;
                display: flex;
                margin-right: 6px;

                @include breakpoint(medium down) {
                    @include size(10px);
                }

                &.is-active {
                    width: 40px;

                    @include breakpoint(medium down) {
                        width: 26px;
                    }

                    .swiper-pagination__bullet-btn {
                        background-color: var(--color-1--1);
                        border: 1px solid var(--color-1--1);
                        width: 100%;
                    }
                }
            }

            &__bullet-btn {
                border: 1px solid $color-3--4;
            }
        }
    }

    &__group {
        @include absolute(null, null, 0, 70px);
        align-items: center;
        display: flex;
        flex-direction: row-reverse;
        z-index: 3;

        @include breakpoint(medium down) {
            bottom: 20px;
            left: 50%;
            right: 50%;
            transform: translate(-50%);
            width: fit-content;
        }

        @include breakpoint(small down) {
            bottom: 4px;
            left: 50%;
        }
    }

    &__control {
        @include size(40px);
        @include absolute(auto !important, null, 75px, null);
        border: 3px solid $color-black;
        border-radius: 50%;
        cursor: pointer;
        z-index: 2;

        @include breakpoint(medium down) {
            @include size(35px);
            bottom: 60px;
        }

        @include breakpoint(small down) {
            bottom: 62px;
        }

        &.is-prev {
            left: 0;

            @include breakpoint(medium down) {
                left: 0;
            }

            @include breakpoint(small down) {
                left: 0;
            }
        }

        &.is-next {
            left: 351px;

            @include breakpoint(medium down) {
                left: auto;
                right: 0;
            }
        }

        span[class*="fa-"] {
            font-weight: var(--fw-bold);
        }

        @include on-event() {
            background-color: $color-black;
            border-color: $color-black;
            color: $color-white;
        }
    }
}

.events-widget {
    &.is-width-33 {
        .section {
            &__title {
                justify-content: flex-start;
            }
            
            &__more-links {
                justify-content: flex-start;
            }
        }
    }
}
