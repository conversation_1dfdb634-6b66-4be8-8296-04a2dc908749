.news-item {
    $this: &;

    @extend %link-block-context;
    display: flex;
    flex-direction: column-reverse;
    margin: 0 auto;

    @include breakpoint(medium down) {
        align-items: flex-start;
        display: flex;
        flex-direction: row-reverse;
    }

    &__image {
        display: block;
        position: relative;
        z-index: -1;

        @include breakpoint(medium down) {
            flex-shrink: 0;
            max-width: 266px;
        }

        @include breakpoint(small down) {
            max-width: 31.5%;
        }

        img {
            @include object-fit();
            @include size(100%);

            @include breakpoint(small down) {
                height: auto;
            }
        }
    }

    &__content {
        @include line-decor(35px, 4px, "after");
        background-color: $color-white;
        padding: 21px 35px 25px 0;

        @include breakpoint(medium down) {
            flex-grow: 1;
            margin: 0;
            max-width: 100%;
            padding: 0 11px;
        }

        @include breakpoint(small down) {
            max-width: 68.5%;
            padding: 0 0 0 10px;
        }

        &::after {
            content: none;
        }

        > *:last-child {
            margin-bottom: 0;
        }
    }

    &__title {
        &.item-title {
            font-size: 2.2rem;
            line-height: 28px;

            @include breakpoint(medium down) {
                font-size: 1.7rem;
                line-height: 20px;
                margin-bottom: 5px;
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__publication-date {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        letter-spacing: 0;
        line-height: 19px;

        @include breakpoint(medium down) {
            font-size: 1.3rem;
            font-weight: var(--fw-medium);
            line-height: 16px;
        }
    }

    &__category {
        margin-bottom: 3px;
        position: relative;

        @include breakpoint(medium down) {
            margin-bottom: 5px;
        }

        &::before {
            @include absolute(-2px, null, null, 7px);
            @include size(2px, 28px);
            background-color: var(--color-2--1);
            content: "";
            rotate: 25deg;

            @include breakpoint(medium down) {
                @include size(3px, 15px);
            }
        }

        &.theme {
            font-size: 1.3rem;
            letter-spacing: 0;
            padding-left: 20px;
            text-transform: uppercase;

            @include breakpoint(medium down) {
                font-size: 1.1rem;
                line-height: 13px;
            }
        }
    }

    &__actions {
        @include absolute(-5px, -5px, null, null);
        z-index: 11;
    }

    .is-sitefactory & {
        margin: 0 0 40px;

        &__category {
            background-color: var(--color-1--1);
        }

        @include breakpoint(small down) {
            margin-top: 40px;
        }
    }

    // Widget 33%
    .is-width-33 & {
        #{$this}__content {
            @include breakpoint(large) {
                margin: -61px auto 0;
                max-width: calc(100% - 74px);
                padding: 34px 30px 50px;
                text-align: center;

                &::after {
                    left: 50%;
                    transform: translate(-50%, calc(50px - 100%));
                }
            }
        }
    }

    // Widget 100% on page content and home
    .news-home &,
    .news-content & {
        #{$this}__content {
            @include breakpoint(large) {
                margin-top: -44px;
                max-width: 319px;
                padding: 34px 37px 0 0;
            }
        }
    }

    .news-home & {
        @include breakpoint(medium down) {
            justify-content: flex-end;
        }

        #{$this}__image {
            @include breakpoint(medium down) {
                @include size(100px, 67px);
            }
        }
    }

    .page-content & {
        #{$this} {
            &__content {
                @include breakpoint(medium down) {
                    padding: 30px 0 30px 40px;
                }

                @include breakpoint(small down) {
                    padding: 0 0 10px 10px;
                }
            }

            &__category {
                font-size: 1.3rem;

                @include breakpoint(small down) {
                    font-size: 1.2rem;
                }
            }

            &__title {
                &.item-title {
                    @include breakpoint(medium down) {
                        font-size: 2.2rem;
                    }

                    @include breakpoint(small down) {
                        font-size: 1.8rem;
                    }
                }
            }
        }
    }
}
