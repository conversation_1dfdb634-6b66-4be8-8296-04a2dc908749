.go-to-top {
    &__link {
        @include size(55px);
        @include fixed(null, 7px, 0, null);
        @include font(null, 1.2rem, var(--fw-normal));
        @include trs;
        align-items: center;
        background-color: var(--color-1--2);
        color: $color-white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        opacity: 1;
        text-decoration: none;
        visibility: visible;
        z-index: 19;

        @include on-event {
            background-color: var(--color-2--2);
            color: $color-black;
        }

        &.is-hidden {
            display: none;
            opacity: 0;
            visibility: hidden;
        }
    
        span[class*=fa-] {
            font-size: 1.8rem;
            margin-top: 5px;
        }
    }

    &__text-link {
        font-size: 1.3rem;
        margin-top: 5px;
    }
}
