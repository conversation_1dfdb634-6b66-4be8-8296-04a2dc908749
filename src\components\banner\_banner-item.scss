.banner-item {
    $this: &;

    @extend %link-block-context;
    align-items: center;
    display: flex;
    flex-direction: row-reverse;
    position: relative;
    z-index: 1;

    @include breakpoint(small down) {
        flex-direction: column-reverse;
    }

    @include breakpoint(small down, true) {
        flex-direction: column-reverse;

        &.is-reverse {
            flex-direction: column;
        }
    }

    &__image {
        @include size(100%, 374px);
        margin-left: auto;
        margin-right: 0;
        max-width: 100%;

        @include breakpoint(medium down) {
            @include size(100%, 182px);
            margin-right: 0;
        }

        @include breakpoint(small down) {
            @include size(320px, 133px);
            margin-right: auto;
        }

        img {
            @include object-fit();
            @include size(100% !important);
        }
    }

    &__content {
        align-items: flex-end;
        clip-path: polygon(0 0, 100% 0, 100% 100%, 13% 100%);
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        margin: 0 0 0 -174px;
        min-height: 250px;
        min-width: 50%;
        padding: 40px 50px 40px 107px;
        position: relative;
        z-index: 2;

        @include breakpoint(medium down) {
            margin: 0 0 0 -62px;
            min-height: 92px;
            padding: 18px 22px 18px 75px;
            width: 635px;
        }

        @include breakpoint(small down) {
            margin-left: auto;
            margin-top: -21px;
            max-width: 280px;
            padding: 28px 48px 28px 50px;
        }

        span[class*="fa-"] {
            color: var(--color-2--1);
            margin-left: 10px;

            @include breakpoint(small down) {
                left: auto;
                right: 0;
                rotate: 0deg;
            }
        }

        &::before {
            @include absolute(14px, null, null, 125px);
            @include size(645px, 100%);
            background-image: image("decor-bandeaupromo.svg");
            background-repeat: no-repeat;
            background-size: cover;
            content: "";
            opacity: 0.2;
            z-index: -1;

            @include breakpoint(medium down) {
                @include size(225px, 70px);
                left: 76px;
                top: 8px;
            }

            @include breakpoint(small down) {
                left: 40px;
                top: 6px;
            }
        }

        .is-inverted & {
            color: $color-white;
        }
    }

    @include fa-icon-style(false) {
        @include trs;
        font-size: 3rem;
        font-weight: var(--fw-normal);

        @include breakpoint(medium down) {
            font-size: 1.6rem;
        }
    }

    &__title {
        @include font(var(--typo-1), 4.5rem, var(--fw-bold));
        display: inline;
        line-height: 50px;
        max-width: 600px;

        @include breakpoint(medium down) {
            font-size: 2.1rem;
            line-height: 24px;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }

        .underline {
            .is-inverted & {
                @include multiline-underline();
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &:focus-visible {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &.is-reverse {
        flex-direction: row;

        @include breakpoint(small down) {
            flex-direction: column-reverse;
        }

        @include fa-icon-style(false) {
            color: var(--color-2--1);
            margin-left: 25px;
            margin-right: 0;
            rotate: 0deg;
        }

        #{$this}__content {
            clip-path: polygon(0 0, 100% 0, 92% 100%, 0% 100%);
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            margin: 0 -171px 0 0;
            max-width: 100%;
            min-width: 50%;
            padding: 50px 99px 50px 60px;

            @include breakpoint(medium down) {
                margin: 0 -62px 0 0;
                max-width: max-content;
                padding: 18px 66px 18px 62px;
            }

            @include breakpoint(small down) {
                clip-path: polygon(0 0, 100% 0, 83% 100%, 0% 100%);
                margin-left: 0;
                margin-right: auto;
                margin-top: -21px;
                max-width: 280px;
                padding: 18px 61px 26px 21px;
            }

            &::before {
                left: 5px;

                @include breakpoint(medium down) {
                    @include size(225px, 70px);
                    left: 76px;
                    top: 8px;
                }

                @include breakpoint(small down) {
                    left: -40px;
                    top: 6px;
                }
            }
        }

        #{$this}__image {
            margin-left: 0;
            margin-right: auto;

            @include breakpoint(medium down) {
                margin-left: 0;
            }

            @include breakpoint(small down) {
                margin-left: auto;
            }
        }

        #{$this}__title {
            margin-left: 0;
        }
    }

    .is-width-33 & {
        @include breakpoint(large) {
            flex-direction: column-reverse;

            &.is-reverse {
                flex-direction: column;

                @include fa-icon-style(false) {
                    bottom: 195px;
                }

                #{$this}__image {
                    margin-left: auto;
                    margin-top: 0;
                }
            }

            #{$this}__content {
                @include size(100%, auto);
                clip-path: none;
                margin: 0;
                max-width: max-content;
                min-height: auto;
                padding: 37px 59px 37px 41px;

                &::before {
                    @include size(242px, 100px);
                }
            }

            #{$this}__title {
                font-size: 2.4rem;
                line-height: calc(28 / 24);
            }

            #{$this}__image {
                @include size(100%, 133px);
                margin-right: auto;
                margin-top: 0;

                img {
                    @include object-fit();
                    @include size(100% !important);
                }
            }

            @include fa-icon-style(false) {
                bottom: 35px;
                font-size: 1.9rem;
                right: 28px;
            }
        }
    }
}
