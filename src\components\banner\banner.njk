{%- from 'views/core-components/section.njk' import Section -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{% from 'views/core-components/image.njk' import Image %}
{%- from 'views/core-components/link.njk' import Link -%}

{%- macro BannerItem(
    isReverse = false,
    isInverted = false,
    bgColor = '#c5c5c5',
    title = 'Découvrez CCCA-BTP.tv',
    imageSizes = ['320x135?479', '440x182?1279', '900x374']
    ) -%}
    <div class="banner-item {{ 'is-reverse' if isReverse }} {{ 'is-inverted' if isInverted }}">
        <div class="banner-item__content" style="background-color: {{ bgColor }};">
            <h2 class="banner-item__title">
                <a href="#" class="banner-item__title-link">
                    <span class="underline">{{ title }}</span>
                </a>
            </h2>
            {{ Icon('far fa-long-arrow-right') }}
        </div>
        {{ Image({
            className: "banner-item__image",
            sizes: imageSizes,
            serviceID: range(100) | random,
            alt: 'image alt'
        }) }}
    </div>
{%- endmacro -%}

{%- macro BannerHome(
    isReverse = false,
    isInverted = false,
    bgColor = '#c5c5c5'
    ) -%}
    {% call Section(className = 'banner-home', container = 'banner-home__container') %}
        <div class="section__content banner-home__content">
            {{ BannerItem(
                isReverse = isReverse,
                isInverted = isInverted,
                bgColor = bgColor
            ) }}
        </div>
        <div class="section__more-links banner-home__more-links  {{ 'is-reverse' if isReverse }}">
            {{ Link(
                href = "#",
                text = 'VOIR TOUTES NOS VIDÉOS',
                className = 'btn is-primary is-sm-small',
                icon = "far fa-brands fa-youtube"
            ) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{%- macro BannerContent(
    className = 'banner-content',
    isReverse = false,
    isInverted = false,
    bgColor = '#c5c5c5',
    imageSizes = ['320x135?479', '440x182?1279', '900x374']
    ) -%}
    {% call Section(className = className, container = false) %}
        <div class="section__content">
            {{ BannerItem(
                isReverse = isReverse,
                isInverted = isInverted,
                bgColor = bgColor,
                imageSizes = imageSizes
            ) }}
        </div>
    {% endcall %}
{%- endmacro -%}
