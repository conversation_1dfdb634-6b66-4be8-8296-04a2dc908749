.header-nav {
    $this: &;

    padding: 0;
    width: 100%;

    @include breakpoint(medium down) {
        display: none;
    }

    &__list {
        align-items: center;
        display: flex;
        justify-content: space-between;
    }

    &__item {
        margin-right: 44px;
        position: relative;

        &::before {
            @include size(1px, 38px);
            @include absolute(50%, -15px, null, null);
            background-color: $color-black;
            content: "";
            rotate: 23deg;
            transform: translateY(-50%);
        }

        &:last-child {
            margin-right: 0;

            &::before {
                content: none;
            }

            .header.has-nav-bottom & {
                #{$this}__link {
                    &::after {
                        content: none;
                    }
                }
            }
        }
    }

    &__link {
        @include trs;
        align-items: center;
        background: none;
        border: 0;
        color: var(--color-1--1);
        cursor: pointer;
        display: flex;
        font-size: 1.5rem;
        font-weight: 700;
        justify-content: center;
        letter-spacing: 0;
        line-height: 19px;
        max-width: 143px;
        padding: 0;
        position: relative;
        text-align: left;
        text-decoration: none;
        text-transform: uppercase;

        @include on-event {
            color: $color-black;
            text-decoration: underline;

            &::after {
                opacity: 0;
            }
        }

        .header.has-nav-bottom & {
            color: $color-black;

            @include on-event {
                color: $color-black;

                &::after {
                    opacity: 0;
                }
            }

            &::after {
                background-color: $color-white;
            }
        }

        .header:not(.js-fixed-el):not(.has-nav-bottom) & {
            .has-page-image:not(.has-secondary-heading) &,
            body.home-page &,
            body.home-hospital-page & {
                color: $color-white;

                &::after {
                    background-color: $color-white;
                }
            }
        }
    }
}
