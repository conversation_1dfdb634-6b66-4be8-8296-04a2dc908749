.main-nav-toggle {
    $this: &;

    @include size(80px);
    @include trs;
    align-content: center;
    align-items: center;
    border: 1px solid $color-black;
    color: var(--color-1--2);
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 20px;
    position: relative;

    @include breakpoint(medium down) {
        @include size(63px);
        padding: 0;
    }

    @include breakpoint(small down) {
        @include size(45px);
    }

    @include on-event {
        @include breakpoint(large only) {
            #{$this}__text {
                font-size: 1.2rem;
            }

            #{$this}__bar {
                &:nth-child(1) {
                    top: -3px;
                }

                &:nth-child(2) {
                    opacity: 0;
                }

                &:nth-child(3) {
                    top: 23px;
                }
            }
        }
    }

    &__bars {
        @include size(17px, 22px);
        position: relative;
    }

    &__bar {
        @include trs;
        @include size(17px, 2px);
        @include absolute(50%, null, null, 50%);
        background-color: var(--color-1--2);
        transform: translateX(-50%);

        &:nth-child(1) {
            top: 3px;
        }

        &:nth-child(2) {
            opacity: 1;
            top: 9px;
        }

        &:nth-child(3) {
            top: 15px;
        }
    }

    &__text {
        @include trs;
        color: inherit;
        font-size: 0;
        font-weight: var(--fw-normal);
        position: absolute;
    }

    &.is-open {
        @include breakpoint(large only) {
            #{$this}__bar {
                left: 0;

                &,
                &:first-child,
                &:last-child {
                    opacity: 0;
                    top: 50%;
                }

                &:first-child {
                    opacity: 1;
                    transform: translateY(-50%) rotate(45deg);
                }

                &:last-child {
                    opacity: 1;
                    transform: translateY(-50%) rotate(-45deg);
                }
            }

            #{$this}__text {
                opacity: 0;
            }
        }
    }
}
