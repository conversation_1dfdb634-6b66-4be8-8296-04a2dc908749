.key-box-item {
    display: flex;
    text-decoration: none;

    @include breakpoint(medium down) {
        margin: 0;
        max-width: 768px;
    }

    @include breakpoint(small down) {
        margin-bottom: 30px;
    }

    &__icon {
        @include size(84px);
        display: flex;
        justify-content: center;
        margin: 0 auto;

        @include breakpoint(small down) {
            @include size(58px);
        }

        svg {
            @include size(100%);
            fill: var(--color-1--1);
        }
    }

    &__title {
        color: $color-black;
        font-size: 3.5rem;
        font-weight: var(--fw-black);
        line-height: 5rem;

        @include breakpoint(small down) {
            font-size: 2.8rem;
        }

        strong {
            font-weight: inherit;
        }
    }

    &__description {
        font-size: 1.9rem;
        line-height: 2.4rem;
        max-width: 60%;
        position: relative;

        @include breakpoint(medium down) {
            max-width: 100%;
        }

        @include breakpoint(small down) {
            font-size: 1.6rem;
        }

        &::after {
            @include size(4px, 28px);
            @include absolute(null, null, -41px, 7px);
            background-color: var(--color-2--1);
            content: "";
            rotate: 26deg;
        }
    }

    &__content {
        margin-left: 20px;
        text-decoration: none;
        width: calc(100% - 85px);

        @include breakpoint(small down) {
            width: 100%;
        }

        @include on-event() {
            text-decoration: underline;
        }
    }
}

.key-box-home {
    .section {
        &__title {
            margin-bottom: 51px;
            margin-left: 40px;

            @include breakpoint(medium down) {
                margin-left: 33px;
            }
        }

        &__content {
            .list__item {
                padding: 0;

                @include breakpoint(medium down) {
                    margin-bottom: 30px;
                    padding-bottom: 34px;
                    padding-left: 15px;
                }

                @include breakpoint(small down) {
                    padding-bottom: 30px;
                }

                &:last-of-type {
                    @include breakpoint(small down) {
                        margin-bottom: 0;
                        padding-bottom: 0;
                    }
                }
            }
        }
    }

    &__container {
        @extend %container;

        @include breakpoint(medium down) {
            max-width: 768px;
            padding: 0 40px;
        }

        @include breakpoint(small down) {
            max-width: 360px;
            padding: 0 20px;
        }
    }
}

.key-box {
    .section {
        &__content {
            .list__item {
                padding: 0;

                @include breakpoint(medium down) {
                    margin-bottom: 30px;
                    padding-bottom: 34px;
                    padding-left: 15px;
                }

                @include breakpoint(small down) {
                    padding-bottom: 30px;
                }

                &:last-of-type {
                    @include breakpoint(small down) {
                        margin-bottom: 0;
                        padding-bottom: 0;
                    }
                }
            }
        }
    }
}
