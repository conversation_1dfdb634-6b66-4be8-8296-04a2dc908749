// Footer styles

.footer {
    &__wrapper {
        background-color: $color-3--1;
        overflow: hidden;
        position: relative;
    }

    &__container {
        @extend %container;
        padding-bottom: 75px;
        padding-top: 65px;

        @include breakpoint(medium down) {
            padding: 64px 62px 59px;
        }

        @include breakpoint(small down) {
            padding: 51px 33px 46px;
        }
    }

    &__row {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        @include breakpoint(medium down) {
            flex-wrap: wrap;
        }

        @include breakpoint(small down) {
            display: block;
        }
    }

    &__column {
        @include breakpoint(medium down) {
            margin: 0;
            max-width: 768px;
        }

        @include breakpoint(small down) {
            max-width: 320px;
        }

        &.is-first {
            @include breakpoint(medium down) {
                padding: 0;
                width: 100%;
            }
        }

        &.is-second {
            margin-left: 280px;
            margin-top: 31px;
            padding: 0;
            position: relative;
            width: 820px;

            @include breakpoint(medium down) {
                margin-left: 0;
                margin-top: 25px;
                padding: 0;
                width: 100%;
            }

            @include breakpoint(small down) {
                margin-top: 45px;
            }
        }
    }

    &__bottom {
        align-items: center;
        background-color: $color-3--1;
        border-top: 1px solid $color-3--2;
        display: flex;
        padding-bottom: 75px;

        @include breakpoint(medium down) {
            flex-direction: column;
            padding: 27px 0 22px;
        }

        @include breakpoint(small down) {
            padding: 27px 0 40px;
        }
    }

    &__rgaa {
        @include trs;
        color: $color-black;
        display: block;
        flex-shrink: 0;
        font-size: 14px;
        line-height: 0.5;
        min-width: 142px;
        padding: 23px 0 24px 45px;
        text-decoration: none;

        @include breakpoint(medium down) {
            min-width: unset;
            padding: 0;
            width: initial;
        }

        &[href] {
            @include on-event {
                color: var(--color-1--1);
            }
        }

        @include add-inverted-styles {
            color: $color-white;

            &[href] {
                @include on-event {
                    color: var(--color-2--1);
                }
            }
        }
    }

    &__rgaa-caption {
        font-size: 11px;
        line-height: 1;
    }

    &__rgaa-status {
        font-weight: bold;
    }

    &__rgaa-status-wrapper {
        display: block;
        margin-top: 1px;
    }

    //name Stratis signature
    &__stratis {
        @include font(var(--typo-1), 1.1rem, var(--fw-bold));
        background-color: transparent;
        color: $color-white;
        flex-shrink: 0;
        line-height: 1.6rem;
        margin: 0;
        text-align: right;
        text-transform: uppercase;
        z-index: 5;

        a {
            @include trs;
            background: var(--color-1--1);
            color: $color-white;
            display: inline-block;
            padding: 3px 14px;
            text-decoration: none;

            &:hover,
            &:focus {
                background: $color-white;
                color: var(--color-1--1);
            }
        }
    }

    @include add-inverted-styles {
        &__wrapper {
            background-color: var(--color-1--2);
        }

        &__bottom {
            background-color: var(--color-1--2);
            border: 0;
        }
    }
}
