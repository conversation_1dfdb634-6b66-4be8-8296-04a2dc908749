{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/infos.njk' import InfoBlock -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    OrganigrammeItem template.
    @param {object} settings - organigram item settings.
#}
{%- macro OrganigrammeItem(
    imageSizes = ['180x180'],
    tag = 'h3',
    className = ''
) -%}
    <div class="organigramme-item js-organigramme-item {{ className }}">
        {{ Image({
            className: 'organigramme-item__picture avatar-image',
            image: ['organigramme-placeholder.svg'],
            alt: ''
        }) }}
        <div class="organigramme-item__content">
            <div class="organigramme-item__content-top">
                <{{ tag }} class="organigramme-item__name">
                    <span class="organigramme-item__position">Thématique</span>
                    <span class="sr-only"> : </span>
                    <a href="#" class="organigramme-item__name-link" data-filter="s">
                        <span class="underline">{{ lorem(1, 'words') }}</span>
                    </a>
                </{{ tag }}>
            </div>
            <div class="organigramme-item__content-info">
                <div class="organigramme-item__details">
                    {%- for item in range(3) %}
                        <p class="organigramme-item__function {{ 'is-main' if loop.first }}" data-filter="fonction">
                            Fonction {{ loop.index }} {{ lorem(3, 'words') }}
                        </p>
                    {%- endfor %}
                </div>
                <ul class="organigramme-item__infos">
                    {% set links = [
                        ['04 65 71 52 33', 'Téléphone', 'far fa-phone', 'tel:0465715233'],
                        ['Courriel', null, 'far fa-at', 'mailto:<EMAIL>']
                    ] %}
                    {% for text, textSrOnly, icon, link in links %}
                        <li class="organigramme-item__infos-item">
                            {{ Link(
                                className = 'btn is-small',
                                text = text,
                                textSrOnly = textSrOnly,
                                href = link,
                                icon = icon
                            ) }}
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
{%- endmacro -%}

{#
    OrganigrammeContent template.
    Template for organigramme on page-content.
    @param {string} titleText - section title
    @param {number} itemsCount - count of organigramme
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{%- macro OrganigrammeContent(
    titleText = 'Organigramme',
    itemsCount = 2,
    moreButton = false,
    proposerButton = false
    ) -%}
    {% call Section(className = 'organigramme-content', container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText,
                iconPath = false
            ) }}
        </div>
        <div class="section__content">
            {% call List(
                count = itemsCount,
                cols = 2,
                mdCols = 1,
                smCols = false,
                xsCols = false
                ) %}
                {{ OrganigrammeItem() }}
            {% endcall %}
        </div>
        {% if moreButton or proposerButton %}
            <div class="section__more-links">
                {% if proposer %}
                    {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un organigramme',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
                {% endif %}
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les organigramme',
                        className = 'btn',
                        icon = 'far fa-plus'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    OrganigrammeList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro OrganigrammeList(
    listClass = '',
    itemClass = 'has-mb-6',
    tagItem = 'h3',
    cols = 3,
    mdCols = 2,
    smCols = 1,
    xsCols = 1,
    count = 6,
    primary = false
    ) -%}
    {% call List(
        itemClass = itemClass,
        tagItem = tagItem,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols,
        listClass = listClass,
        count = count
        ) %}
        {{ OrganigrammeItem(className = 'is-primary' if primary, tag = tagItem,theme= lorem(1, 'words')+', '+lorem(2, 'words')+','+lorem(1, 'words')) }}
    {% endcall %}
{%- endmacro -%}
