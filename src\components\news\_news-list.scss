.news-list {
    $this: &;

    &.space {
        margin-bottom: -90px !important;
    }

    &__item {
        margin-bottom: 24px;

        @include breakpoint(medium down) {
            margin-bottom: 10px;
        }

        @include breakpoint(small down) {
            margin-bottom: 22px;
        }
    }

    .news-home & {
        display: grid;
        gap: 0;
        grid-auto-rows: 1fr; /* chaque ligne automatique aura la même hauteur */
        grid-template-columns: repeat(3, 1fr);
        min-height: 744px;

        @include breakpoint(medium down) {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start !important;
            min-height: auto;
        }

        @include breakpoint(small down) {
            flex-direction: column;
        }

        &::before {
            content: none;
        }

        #{$this}__item {
            @include breakpoint(large only) {
                &:nth-of-type(1) {
                    grid-area: 1 / 3 / 2 / 4;
                }

                &:nth-of-type(2) {
                    grid-area: 2 / 3 / 3 / 4;
                    margin-bottom: 65px;
                }

                &:nth-of-type(3) {
                    grid-area: 3 / 1 / 4 / 2;
                }

                &:nth-of-type(4) {
                    grid-area: 3 / 2 / 4 / 3;
                }

                &:nth-of-type(5) {
                    grid-area: 3 / 3 / 4 / 4;
                }
            }

            @include breakpoint(medium down) {
                margin-bottom: 20px;
                width: 50%;
            }

            @include breakpoint(small down) {
                width: 100%;
            }
        }
    }
}
