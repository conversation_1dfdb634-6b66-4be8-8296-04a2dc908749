.contact-item {
    $this: &;

    &.is-structure {
        background-color: var(--color-2--1);
        padding: 35px;

        @include breakpoint(medium down) {
            padding: 25px;
        }

        @include breakpoint(small down) {
            padding: 35px 10px;
        }

        #{$this}__picture {
            @include breakpoint(medium down) {
                margin-right: 30px;
                max-width: 200px;
            }

            @include breakpoint(small down) {
                margin: 0 auto 20px;
            }
        }

        #{$this}__content-top {
            @include breakpoint(small down) {
                padding: 0 30px;
            }
        }

        #{$this}__content-info {
            @include breakpoint(small down) {
                margin-top: 10px;
                padding: 0 30px;
            }
        }

        #{$this}__details {
            text-align: left;

            .infos:nth-child(2) {
                margin-top: 30px;

                @include breakpoint(small down) {
                    display: flex;
                    justify-content: center;
                    margin-top: 20px;
                }
            }
        }

        .is-width-66 & {
            @include breakpoint(large) {
                #{$this}__picture {
                    margin-right: 30px;
                    max-width: 200px;
                }

                #{$this}__infos {
                    flex-direction: row;
                    flex-wrap: wrap;
                    margin: 0 -2.5px;
                    width: 400px;
                }

                #{$this}__infos-item {
                    margin: 0 2.5px 5px;
                }
            }
        }

        .is-width-33 & {
            @include breakpoint(large) {
                padding: 20px;

                #{$this}__picture {
                    margin: 0 auto 20px;
                    max-width: 200px;
                }

                #{$this}__content-info {
                    margin-top: 10px;
                    padding: 0 30px;
                }

                #{$this}__details {
                    .infos:nth-child(2) {
                        display: flex;
                        justify-content: center;
                        margin-top: 20px;
                    }
                }
            }
        }
    }
}
