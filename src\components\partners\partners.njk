{%- from 'views/core-components/image.njk' import Image -%}
{% from 'views/core-components/link.njk' import Link %}

{#
    Partners template.
    Template for partners in footer.
#}
{%- macro Partners(
    count = 5
) -%}
    <div class="partners">
        <div class="partners__container">
            <div class="partners__title-container">
                <h2 class="partners__title">Partenaires</h2>
                <div class="partners__more-partners">
                    {{ Link(
                        href = "#",
                        text = 'TOUS LES PARTENAIRES',
                        className = 'btn is-primary is-sm-small',
                        icon = 'far fa-arrow-right'
                    ) }}
                </div>
            </div>

            <div class="partners__content">
                <ul class="partners__list">
                    {% for item in range(count) %}
                        <li class="partners__item">
                            <a href="#" class="partners__link js-tooltip" target="_blank" rel="noreferrer" data-content="lorem ipsum {{ loop.index }}">
                                {{ Image({
                                    className: 'partners__image',
                                    sizes: ['157x99'],
                                    alt: 'lorem ipsum ' + loop.index,
                                    width: 150,
                                    height: 99
                                }) }}
                            </a>
                            <p class="partners__item-title">{{ lorem(3, 'words') }}</p>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
{%- endmacro -%}
