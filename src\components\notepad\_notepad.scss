.notepad-popup {
    @extend %container;
    background-color: $color-white;

    &.fancybox__content {
        max-width: 1200px;
        padding: 80px 50px 50px;
        position: relative;

        @include breakpoint(medium down) {
            max-width: calc(100% - 124px);
            padding: 70px 30px 110px;
        }

        @include breakpoint(small down) {
            max-width: calc(100% - 40px);
            overflow: auto;
        }
    }

    .title__text {
        color: var(--color-1--1);
    }

    input {
        min-height: auto;
        width: auto;
    }

    .popup__close-btn {
        border-color: $color-black !important;
        color: $color-black !important;
        right: 30px;
        top: 30px;
    }

    .actions {
        display: flex;
        justify-content: center;
        margin-top: 33px;

        @include breakpoint(medium down) {
            justify-content: center;
            margin-top: 20px;
            right: 20px;
        }

        @include breakpoint(small down) {
            align-items: flex-start;
            flex-direction: column;
            justify-content: flex-start;
            margin-left: auto;
            margin-top: 20px;
            text-align: right;
            width: 100%;
        }

        .btn {
            @include font(var(--typo-1), 1.4rem, var(--fw-bold));
            background-color: var(--color-2--1);
            border: 1px solid $color-black;
            color: var(--color-1--1);
            margin-right: 20px;
            min-height: auto;
            padding: 20px;

            @include breakpoint(small down) {
                margin-left: 0;
                margin-top: 10px;
                text-align: right;
            }

            @include on-event {
                text-decoration: underline;
            }

            &.delete-btn {
                @include icon-before($fa-var-times);

                &::before {
                    margin-right: 5px;
                    text-decoration: none;
                }
            }
        }
    }

    .table-responsive {
        margin-bottom: 0;
    }
}

.notepad {
    &__pdf {
        @include absolute(114px, 115px);

        @include breakpoint(medium down) {
            right: 94px;
            top: 99px;
        }

        @include breakpoint(small down) {
            top: 95px;
        }
    }
}

.favorites-table {
    --at-min-width: 640px;
    --at-last-column-min-width: 20px;

    @include breakpoint(medium down) {
        margin-top: 61px;
    }

    @include breakpoint(small down) {
        margin-top: 91px;
    }

    &__type,
    &__title,
    &__content {
        @include font(var(--typo-1), 1.4rem, var(--fw-medium));
    }

    &__type {
        min-width: auto;
        width: 118px;

        &.row-grey {
            background-color: $color-3--1;
            color: $color-black;
        }

        &.row-black {
            background-color: $color-3--2;
            color: $color-black;
        }
    }

    &__title-content {
        min-width: auto;
        width: 640px;

        &.row-grey {
            background-color: $color-3--1;
            color: $color-black;
        }

        &.row-black {
            background-color: $color-3--2;
            color: $color-black;
        }
    }

    &__content {
        margin-top: 10px;
    }

    &__header-actions {
        align-items: center;
        display: flex;
        margin-top: 40px;

        @include breakpoint(small down) {
            margin-top: 0;
        }

        label {
            @include font(var(--typo-1), 1.4rem, var(--fw-medium));
            color: $color-3--5;
            margin-left: 5px;
        }
    }

    &__view {
        min-width: auto;
        width: 155px;

        &.row-grey {
            background-color: $color-3--1;
            color: $color-black;
        }

        &.row-black {
            background-color: $color-3--2;
            color: $color-black;
        }
    }

    &__select {
        min-width: auto;
        width: 20px;

        &.is-selects {
            display: flex;
            justify-content: space-between;
            padding: 31px;
            width: 100%;
        }

        &.row-grey {
            background-color: $color-3--1;
            color: $color-black;
        }

        &.row-black {
            background-color: $color-3--2;
            color: $color-black;
        }
    }

    &__header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 22px;

        @include breakpoint(medium down) {
            @include absolute();
            margin-bottom: 21px;
            width: calc(100% - 62px);
        }

        @include breakpoint(small down) {
            flex-direction: column;
        }
    }

    &__header-title {
        @include font(var(--typo-1), 4.5rem, var(--fw-bold));
        color: $color-black;
        line-height: 50px;

        @include breakpoint(medium down) {
            font-size: 3.8rem;
        }

        @include breakpoint(small down) {
            font-size: 2.8rem;
        }
    }

    td {
        border: 1px solid $color-3--1;
        line-height: 18px;
        min-height: 38px;
        padding: 8px 20px;
        text-align: left;

        &:nth-child(2) {
            min-width: var(--at-min-width);
        }

        &:last-child {
            min-width: var(--at-last-column-min-width);
        }
    }

    tr {
        @include on-event() {
            background-color: $color-3--1 !important;
        }
    }

    tbody {
        tr:nth-child(even) {
            background-color: $color-3--2;
        }
    }

    .view-link {
        text-decoration: underline;
    }

    .delete-link {
        color: $tarteaucitron-refuse;
        cursor: pointer;
        font-weight: bold;
        text-decoration: none;
    }

    .delete-link:hover {
        text-decoration: underline;
    }
}
