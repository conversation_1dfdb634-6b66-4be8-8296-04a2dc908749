.publications-content {
    .section {
        &__content {
            padding: 0 40px;

            @include breakpoint(medium down, true) {
                padding: 0 17px;
            }

            @include breakpoint(small down, true) {
                padding: 0;
            }
        }

        &__more-links {
            justify-content: flex-start;
            padding-left: 310px;

            @include breakpoint(medium down, true) {
                padding-left: 230px;
            }

            @include breakpoint(small down, true) {
                justify-content: flex-start;
                padding-left: 0;
            }
        }
    }

    &.is-width-33 {
        .section {
            &__title {
                justify-content: flex-start;
            }
        }
        
        .publications-content-item {
            &__title {
                text-align: left;
            }

            &__category {
                text-align: left;
            }

            &__image {
                margin: 0;
            }

            &__publication[class] {
                text-align: left;
            }
        }
    }
}

.publications-home {
    &__container {
        @extend %container;
    }

    .section {
        &__content {
            padding: 0 37px 0 42px;

            @include breakpoint(medium down) {
                padding: 0 37px 0 33px;
            }

            @include breakpoint(small down) {
                padding: 0;
            }
        }

        &__more-links {
            justify-content: flex-start;
            padding-left: 412px;

            @include breakpoint(medium down) {
                padding-left: 240px;
            }

            @include breakpoint(small down) {
                justify-content: center;
                padding-left: 0;
            }

            .btn {
                margin-top: -45px;
                position: relative;
                z-index: 5;

                @include breakpoint(medium down) {
                    margin-top: 35px;
                }

                @include breakpoint(small down) {
                    margin-top: 38px;
                }
            }
        }
    }
}
