.key-module-item {
    $this: &;

    align-items: stretch;
    background-color: $color-3--2;
    display: flex;
    margin-bottom: 30px;
    padding-right: 35px;

    @include breakpoint(medium down) {
        align-items: center;
        flex-direction: column;
        margin: 0 auto 30px;
        max-width: 720px;
        padding: 0 0 35px;
    }

    &.is-secondary {
        align-items: center;
        padding: 35px;

        @include breakpoint(medium down) {
            padding: 35px;
        }

        @include breakpoint(small down) {
            padding: 35px 25px;
        }

        #{$this}__description {
            margin-right: 0;
            order: 3;

            @include breakpoint(medium down) {
                margin-bottom: 0;
                padding: 0;
            }

            @include breakpoint(small down) {
                margin-bottom: 0;
            }
        }

        #{$this}__number-wrapper {
            background-color: var(--color-2--1);
            color: $color-black;
            height: 100%;
            margin-left: -88px;
            order: 2;
            padding: 0 30px;
            z-index: 2;

            @include breakpoint(medium down) {
                margin: 48px 0 0;
                width: auto;
            }

            @include breakpoint(small down) {
                margin: 25px -25px 0;
                padding: 0 29px;
            }
        }

        #{$this}__number {
            color: $color-black;
            z-index: 2;
        }

        #{$this}__image {
            margin: 0;
            order: 1;
            padding: 0;
        }
    }

    &__description {
        display: inline-block;
        flex-grow: 1;
        font-size: 2rem;
        line-height: 3.4rem;
        margin: 0 36px 0 44px;
        padding: 43px 0;
        width: 1%;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
            margin: 0 0 35px;
            padding: 0 49px;
            text-align: center;
            width: 100%;
        }

        @include breakpoint(small down) {
            line-height: 30px;
            padding: 0 30px;
        }
    }

    &__number-wrapper {
        align-items: center;
        background-color: var(--color-1--1);
        display: flex;
        justify-content: center;
        line-height: 50px;
        padding: 0 60px;

        @include breakpoint(medium down) {
            margin-bottom: 54px;
            padding: 36px 0 38px;
            width: 100%;
        }

        @include breakpoint(small down) {
            margin-bottom: 29px;
        }
    }

    &__number {
        color: $color-white;
        font-size: 4.5rem;
        font-weight: var(--fw-bold);
    }

    &__image {
        align-items: center;
        display: flex;

        @include breakpoint(large only) {
            margin: 0 0 0 auto;
            padding: 43px 0;
        }
    }
}
