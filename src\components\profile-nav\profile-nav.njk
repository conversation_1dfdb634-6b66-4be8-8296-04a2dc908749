{% set profileNavLinks = [
    'Jeunes',
    'Famille',
    'Séniors',
    'Nouveaux arrivants',
    'En situation de handicap',
    'Entrepreneurs',
    'Touristes'
] %}

{%- macro ProfileNav(useToggle = false, links = profileNavLinks) -%}
    <nav class="profile-nav {{ 'js-dropdown' if not useToggle }}" role="navigation" aria-label="par profil">
        <button type="button" class="profile-nav__toggle {{ 'js-dropdown-toggle' if not useToggle }}" {{ 'data-sd-toggle=profile-nav' if useToggle }}>
            <span class="btn__svg" aria-hidden="true"></span>
            <span class="profile-nav__toggle-text">Vous êtes</span>
        </button>
        <div class="profile-nav__block {{ 'js-dropdown-block' if not useToggle }}" {{ 'data-sd-content=profile-nav' if useToggle }}>
            <div class="profile-nav__menu">
                <ul class="profile-nav__menu-links">
                    {% for link in links %}
                        <li class="profile-nav__item">
                            <a href="#" class="profile-nav__link">
                                <span class="profile-nav__link-text">{{ link }}</span>
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </nav>
{%- endmacro -%}