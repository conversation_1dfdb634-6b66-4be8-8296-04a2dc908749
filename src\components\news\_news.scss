// News section global
.news-content,
.news-widget {
    .news-list {
        &__item {
            @include breakpoint(large) {
                margin-bottom: 15px;
            }
        }
    }
}

.news-home {
    min-height: 744px;

    &.section {
        @include breakpoint(medium down) {
            margin-top: 45px;
        }
    }

    &__container {
        @extend %container;
        position: relative;

        @include breakpoint(medium down) {
            max-width: 768px;
        }

        @include breakpoint(small down) {
            max-width: 360px;
        }
    }

    &__content {
        @include breakpoint(medium only) {
            padding: 0 22px;
        }
    }

    &__more-links {
        justify-content: flex-end;
        margin-left: auto;
        width: fit-content;

        @include breakpoint(medium down) {
            margin-left: auto;
            margin-right: 22px;
        }

        @include breakpoint(small down) {
            margin-right: 0;
            margin-top: 30px;
        }

        @include on-event() {
            .btn {
                background-color: var(--color-1--2);
                color: $color-white;
            }
        }

        .btn {
            background-color: transparent;
            color: var(--color-1--2);
            font-size: 1.2rem;
            font-weight: var(--fw-medium);
            line-height: 15px;
            min-height: auto;
            padding: 14px 30px;
        }
    }

    &__item {
        &:not(.swiper-slide-active) {
            visibility: hidden;
        }
    }

    .section__title {
        margin-bottom: 45px;

        @include breakpoint(medium down) {
            align-items: flex-start;
            margin-bottom: 39px;
        }

        @include breakpoint(small down) {
            margin-bottom: 22px;
        }

        .title__content {
            margin-top: -10px;

            @include breakpoint(medium down) {
                margin-left: 0;
                margin-top: 0;
                padding-left: 0;
            }
        }
    }
}

.news-home-block {
    @include absolute();
    @include size(792px, fit-content);

    @include breakpoint(medium down) {
        @include size(100%);
        margin-left: auto;
        position: relative;
    }

    &::before {
        @include size(660px, 400px);
        @include absolute(139px, null, null, -4px);
        background-image: image("yellow-motif-container.svg");
        background-repeat: no-repeat;
        background-size: cover;
        content: "";
        transform: translate(-50%);

        @include breakpoint(medium down) {
            @include size(268px, 173px);
            top: 248px;
        }

        @include breakpoint(small down) {
            left: 1px;
            top: 66px;
        }
    }

    @include breakpoint(medium down) {
        margin-bottom: 65px;
    }

    @include breakpoint(small down) {
        margin-bottom: 30px;
    }

    &__container {
        height: 100%;
    }

    &__action {
        @include trs(opacity);
        background: none;
        border: 0;
        color: $color-black;
        cursor: pointer;
        display: inline-block;
        font-size: 1.5rem;
        padding: 0;

        @include on-event {
            opacity: 0.7;
        }

        &.js-is-playing {
            span {
                &::before {
                    @include size(15px !important);
                    margin-top: 1px;
                }
            }
        }

        &.is-active {
            color: var(--color-2--1);
            display: none;
        }

        span {
            pointer-events: none;

            &::before {
                font-weight: var(--fw-bold);
            }
        }
    }

    &__actions {
        display: flex;

        @include breakpoint(medium down) {
            margin-left: 0;
        }
    }

    &__pagination {
        display: flex;

        .swiper-pagination {
            &__bullet {
                @include size(15px);
                border-color: $color-3--4;
                cursor: pointer;
                display: flex;
                margin-right: 6px;

                @include breakpoint(medium down) {
                    @include size(10px);
                }

                &.is-active {
                    width: 40px;

                    @include breakpoint(medium down) {
                        width: 26px;
                    }

                    .swiper-pagination__bullet-btn {
                        background-color: var(--color-1--1);
                        border: 1px solid var(--color-1--1);
                        width: 100%;
                    }
                }
            }

            &__bullet-btn {
                border: 1px solid $color-3--4;
            }
        }
    }

    &__group {
        @include absolute(null, null, 0, 70px);
        align-items: center;
        display: flex;
        flex-direction: row-reverse;
        z-index: 3;

        @include breakpoint(medium down) {
            bottom: -22px;
            left: 57%;
            transform: translate(-50%);
        }

        @include breakpoint(small down) {
            bottom: -9px;
            left: 51%;
        }
    }

    &__control {
        @include size(40px);
        @include absolute(auto !important, null, 79px, null);
        border: 3px solid $color-black;
        border-radius: 50%;
        cursor: pointer;
        z-index: 2;

        @include breakpoint(medium down) {
            @include size(35px);
            bottom: 35px;
        }

        @include breakpoint(small down) {
            bottom: 62px;
        }

        &.is-prev {
            left: 0;

            @include breakpoint(medium down) {
                left: 83px;
            }

            @include breakpoint(small down) {
                left: 0;
            }
        }

        &.is-next {
            left: 411px;

            @include breakpoint(medium down) {
                left: auto;
                right: 0;
            }
        }

        span[class*="fa-"] {
            font-weight: var(--fw-bold);
        }

        @include on-event() {
            background-color: $color-black;
            border-color: $color-black;
            color: $color-white;
        }
    }
}
